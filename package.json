{"name": "tsgv3", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "node dev.js", "dev:react": "node dev.js --mode react", "dev:server": "node dev.js --mode server", "dev:userscript": "node dev.js --mode userscript", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@auth0/auth0-react": "^2.3.0", "@sec-ant/gm-fetch": "^1.2.1", "aes-js": "^3.1.2", "antd": "^5.24.8", "connect-session-sequelize": "^7.1.7", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.18.2", "express-session": "^1.18.1", "get-urls": "^12.1.0", "glob": "^11.0.2", "html-to-image": "^1.11.13", "jquery": "^3.7.1", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "moment": "^2.30.1", "multer": "1.4.5-lts.2", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "openai": "^4.96.2", "path-to-regexp": "^8.2.0", "qs": "^6.14.0", "react": "^19.1.0", "react-contenteditable": "^3.3.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "sequelize": "^6.37.7", "tough-cookie": "^5.1.2", "tsx": "^4.19.3", "unpdf": "^1.0.4", "unplugin-auto-import": "^19.1.2", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/cli": "^7.27.0", "@babel/core": "^7.26.10", "@babel/node": "^7.26.0", "@babel/preset-env": "^7.26.9", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "chrome-remote-interface": "^0.33.3", "concurrently": "^8.2.0", "vite": "^4.4.5", "vite-plugin-monkey": "^3.4.0"}, "engines": {"node": ">=22.0.0"}}