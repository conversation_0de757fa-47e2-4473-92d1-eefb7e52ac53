import React, { useState, useEffect, useRef } from 'react';
import { Space, message } from 'antd';
import '@ant-design/v5-patch-for-react-19';
import _ from 'lodash';
import { sliceContentByIds, getBaseUrl } from './utils/index.js';
import { useStore } from './stores/index.js';
import { useNavigate, BrowserRouter as Router, Routes, Route, Navigate, useLocation, matchPath, useParams } from 'react-router-dom';
// import { useAuth0 } from '@auth0/auth0-react'; // Assuming not used if custom /api/auth/me is primary

// Import components
import ArticleEditPage from './components/ArticleEditPage';
import ArticlePreviewPage from './components/ArticlePreviewPage';
import { LoadingComponent, LoginPage } from './components/Login';
import MainLayout from './components/MainLayout'; // Import MainLayout
import UnpdfTestPage from './components/UnpdfTestPage'; // Import UnpdfTestPage
import HomeRedirect from './components/HomeRedirect'; // Import HomeRedirect

// Wrapper for ArticleEditPage to provide a stable key based on route parameters
const ArticleEditPageWithKey = () => {
  const { '*': doi, uuid: uuidFromParams } = useParams();
  // Ensure currentIdentifier is always a string for the key prop
  const currentIdentifier = uuidFromParams || doi || 'default-article-key';


  // ArticleEditPage will now fetch its own content and userChunkSize from the store
  return <ArticleEditPage key={currentIdentifier} />;
};

// Component for authenticated routes. This component's instance will be stable once rendered.
const AuthenticatedAppRoutes = () => {
  // console.log('[AuthenticatedAppRoutes] Rendering.'); // Log removed
  return (
    <Routes>
      {/* Routes that use MainLayout */}
      <Route element={<MainLayout />}>
        <Route path="/article/:uuid/edit" element={<ArticleEditPageWithKey />} />
        <Route path="/doi/*" element={<ArticleEditPageWithKey />} />
        <Route path="/article" element={<ArticleEditPageWithKey />} /> {/* Uses 'default-article-key' */}
        <Route path="/unpdf-test" element={<UnpdfTestPage />} /> {/* Add unpdf-test route */}
        <Route path="/" element={<HomeRedirect />} /> {/* Redirect to latest article */}
        {/* Handle invalid /article/ path (with trailing slash) - redirect to /article instead of / */}
        <Route path="/article/" element={<Navigate to="/article" replace />} />
        {/* Fallback for any other authenticated paths that should use MainLayout */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Route>
      {/* Routes that DO NOT use MainLayout can be defined here if needed */}
    </Routes>
  );
};

// Main content component handling login checks and routing logic
const AppContent = () => {
  const location = useLocation();
  const params = useParams(); // For accessing route parameters like :uuid or :*

  const user = useStore(state => state.user);
  const setUser = useStore(state => state.setUser);
  const setDefaultApiModel = useStore(state => state.setDefaultApiModel);
  const setSystemPrompt = useStore(state => state.setSystemPrompt);
  const setUserPrompt = useStore(state => state.setUserPrompt);

  const [isAuthCheckLoading, setIsAuthCheckLoading] = useState(true);
  const initialLoadPerformedRef = useRef(false); // Tracks if the initial set of parallel fetches has been initiated

  useEffect(() => {
    const isPreviewPath = matchPath("/article/:uuid", location.pathname);
    // Determine if we are on a path that requires fetching an article for editing
    const editArticleMatch = matchPath("/article/:uuid/edit", location.pathname);
    const editDoiMatch = matchPath("/doi/*", location.pathname);
    // Explicitly check for the "/article" path for new articles, to avoid fetching with 'default-article-key'
    const isNewArticlePath = location.pathname === "/article";

    if (isPreviewPath) {
      setIsAuthCheckLoading(false);
      initialLoadPerformedRef.current = true; // Mark as performed to avoid re-triggering
      return;
    }

    if (initialLoadPerformedRef.current) {
      // If initial load was done, only ensure loading spinner is off if it was on.
      // The user object changing will trigger re-renders and routing decisions, not re-fetches here.
      if (isAuthCheckLoading) setIsAuthCheckLoading(false);
      return;
    }

    setIsAuthCheckLoading(true);
    initialLoadPerformedRef.current = true;

    const abortController = new AbortController();
    const { signal } = abortController;

    const performInitialFetches = async () => {
      try {
        // Fetch /api/me without the main abortSignal, as it controls the others.
        // It can have its own internal timeout or be allowed to complete.
        const meResponse = await fetch('/api/auth/me');
        let userData = null;
        if (meResponse.ok) {
          userData = await meResponse.json();
          setUser(userData);
          if (userData.defaultApiModel && Array.isArray(userData.defaultApiModel) && userData.defaultApiModel.length === 2) {
            setDefaultApiModel(userData.defaultApiModel[0], userData.defaultApiModel[1]);
          }
          if (userData.systemPrompt !== undefined) setSystemPrompt(userData.systemPrompt || '');
          if (userData.userPrompt !== undefined) setUserPrompt(userData.userPrompt || '');
        } else {
          setUser(null); // Explicitly set to null for "not logged in"
          console.log('User not logged in or /api/me failed, aborting other dependent requests.');
          abortController.abort(); // Abort other fetches
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('/api/me fetch aborted');
        } else {
          console.error('Login check failed:', error);
          setUser(null); // Explicitly set to null for "not logged in"
        }
        // Abort others if /api/me itself fails for network reasons etc.
        if (!signal.aborted) { // Check if not already aborted by previous logic
            abortController.abort();
        }
      } finally {
        // Primary auth check is done, this controls the main loading spinner for login page vs app
        setIsAuthCheckLoading(false);
      }
    };

    // --- Initiate all fetches simultaneously ---
    performInitialFetches();

    // Fetch /api/apis (conditionally abortable)
    // No longer passing signal, as fetchApis has its own 'apisInitialized' guard
    useStore.getState().fetchApis().catch(error => {
      // Error handling remains, though AbortError is less likely from here for this specific call
      if (error.name === 'AbortError') console.log('fetchApis aborted (unexpectedly, as signal was removed)');
      else console.error('fetchApis failed:', error);
    });

    // Fetch /api/articles/my (conditionally abortable)
    // This action should internally check if user is available before fetching
    // or handle the abort signal gracefully if user becomes null due to /api/me failure.
    useStore.getState().fetchUserArticles(signal).catch(error => {
        if (error.name === 'AbortError') console.log('fetchUserArticles aborted');
        else console.error('fetchUserArticles failed:', error);
    });

    // Fetch /api/article/xxx/edit (conditionally abortable and if on correct path)
    let articleIdForFetch = null;
    if (editArticleMatch && editArticleMatch.params.uuid) {
        articleIdForFetch = editArticleMatch.params.uuid;
    } else if (editDoiMatch && editDoiMatch.params['*']) {
        // The '*' in "/doi/*" captures the rest of the path.
        // Ensure this captured part is a valid identifier.
        articleIdForFetch = editDoiMatch.params['*'];
    }

    // Only fetch if we have an ID and it's not the generic new article path
    if (articleIdForFetch && !isNewArticlePath) {
        useStore.getState().fetchArticleForEditing(articleIdForFetch, signal).catch(error => {
            if (error.name === 'AbortError') console.log('fetchArticleForEditing aborted');
            else console.error('fetchArticleForEditing for ID', articleIdForFetch, 'failed:', error);
        });
    } else if (isNewArticlePath) {
        // For new article path, ensure content is cleared or set to a default new state
        // This might involve calling setContent([]) and setArticle(null) or a dedicated action.
        // console.log('On new article path, skipping initial fetchArticleForEditing.');
        // useStore.getState().setContent([]); // Example: clear content for new article
        // useStore.getState().setArticle(null); // Example: clear article details
    }

    return () => {
      // 不要在编辑页面之间切换时中止请求
      // 只在真正离开编辑相关页面时才中止
      console.log('[AppContent] Cleanup called, current path:', location.pathname);
      // 暂时注释掉自动中止逻辑，让 useArticleDataFetching 自己管理请求
      // if (!signal.aborted) {
      //   abortController.abort();
      // }
    };
  }, [location.pathname, setUser, setDefaultApiModel, setSystemPrompt, setUserPrompt]);

  // If still performing the initial auth check (and not on a public preview page), show loading.
  // Show loading if auth check is in progress OR if user state is still undefined (initial state)
  // and not on a public preview page.
  if ((isAuthCheckLoading || user === undefined) && !matchPath("/article/:uuid", location.pathname)) {
    return <LoadingComponent />;
  }

  // After loading and user state is determined (not undefined), decide to show LoginPage or AuthenticatedAppRoutes.
  return (
    <Routes>
      {/* Publicly accessible preview route */}
      <Route path="/article/:uuid" element={<ArticlePreviewPage />} />

      {/* All other routes are caught by "/*" */}
      <Route
        path="/*"
        element={
          user ? <AuthenticatedAppRoutes /> : <LoginPage /> // user will be null if not logged in, or user object if logged in
        }
      />
    </Routes>
  );
};

// Root App component, simply sets up the Router.
const App = () => {
  const isImportingPdf = useStore(state => state.isImportingPdf);
  const importProgressMessage = useStore(state => state.importProgressMessage);
  const cancelPdfImport = useStore(state => state.cancelPdfImport);
  const canRetryFinalizePdf = useStore(state => state.canRetryFinalizePdf);
  const triggerPdfImportRetry = useStore(state => state.triggerPdfImportRetry);
  const setShouldSkipAiParsing = useStore(state => state.setShouldSkipAiParsing);

  return (
    <Router>
      <div style={{ position: 'relative', height: 'auto', minHeight: '100vh', overflow: 'hidden' }}>
        <AppContent />
        {isImportingPdf && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.45)',
              zIndex: 2000,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '20px',
              boxSizing: 'border-box',
            }}
          >
            <span style={{ color: 'white', fontSize: '16px', marginBottom: '20px', textAlign: 'center' }}>
              {importProgressMessage || '正在处理导入，请稍候...'}
            </span>
            <div style={{ display: 'flex', gap: '10px', marginTop: '0px' }}> {/* Adjusted marginTop from 20px to 0px as span already has marginBottom */}
              <button
                onClick={cancelPdfImport}
                style={{
                  padding: '8px 16px',
                  fontSize: '14px',
                  color: '#fff',
                  backgroundColor: '#ff4d4f', // Ant Design danger color
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                }}
              >
                取消导入
              </button>
              {/* 只在AI处理阶段显示跳过解析按钮 */}
              {importProgressMessage && (importProgressMessage.includes('AI处理') || importProgressMessage.includes('准备处理')) && (
                <button
                  onClick={() => {
                    console.log('跳过解析按钮被点击');
                    const storeState = useStore.getState();
                    console.log('当前导入状态:', {
                      isImportingPdf,
                      importProgressMessage,
                      currentImportController: !!storeState.currentImportController
                    });
console.log('当前导入状态的 controller.signal.aborted:', storeState.currentImportController?.signal?.aborted);
                    setShouldSkipAiParsing(true);
const controller = storeState.currentImportController;
                    if (controller && typeof controller.abort === 'function') {
                      console.log('调用 currentImportController.abort() 来中止AI解析');
                      controller.abort();
                    } else {
                      console.warn('无法调用 abort()：currentImportController 不存在或没有 abort 方法。');
                    }

                    console.log('设置shouldSkipAiParsing为true');
                    // 验证状态是否设置成功
                    setTimeout(() => {
                      const currentState = useStore.getState().shouldSkipAiParsing;
                      console.log('验证状态设置结果:', currentState);
                      console.log('当前完整store状态:', useStore.getState());
                    }, 100);
                  }}
                  style={{
                    padding: '8px 16px',
                    fontSize: '14px',
                    color: '#fff',
                    backgroundColor: '#faad14', // Ant Design warning color (orange)
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                  }}
                >
                  跳过解析
                </button>
              )}
              {canRetryFinalizePdf && (
                <button
                  onClick={triggerPdfImportRetry}
                  style={{
                    padding: '8px 16px',
                    fontSize: '14px',
                    color: '#fff',
                    backgroundColor: '#1890ff', // Ant Design primary color (blue)
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                  }}
                >
                  重试
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </Router>
  );
};

export default App;
