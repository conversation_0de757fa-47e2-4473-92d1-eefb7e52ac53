import { App, Modal, Input as AntInput } from 'antd';
import { LinkOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useStore } from '../stores/index.js';
import EnhancedTextArea from '../components/EnhancedTextArea';

export const useArticleCreation = () => {
  const navigate = useNavigate();
  const { message: messageApiApp } = App.useApp();
  const createNewBlankArticleStore = useStore(state => state.createNewBlankArticle);
  const fetchUserArticlesStore = useStore(state => state.fetchUserArticles); // 用于 URL 添加后刷新

  const handleAddFromUrl = () => {
    Modal.confirm({
      title: '从 URL 添加文章',
      icon: <LinkOutlined />,
      content: (
        <EnhancedTextArea
          rows={4}
          placeholder="请输入文章的URL，例如 https://www.example.com/article.html 或 arxiv DOI (例如 10.48550/arXiv.2305.12810)"
          id="add-url-input-sidebar" // 确保 ID 唯一性，如果 Sidebar 中还有其他 Input
        />
      ),
      onOk: () => {
        const urlInput = document.getElementById('add-url-input-sidebar');
        const url = urlInput ? urlInput.value : '';
        if (url) {
          // 清理旧的文章状态，为新文章加载做准备
          useStore.setState({
            article: null,
            content: [],
            translated: {},
            currentTranslationControllers: new Map(),
            isTranslatingAllActive: false,
            isTranslationGloballyCancelled: false,
            indicesToTranslate: [],
            streamingChunkText: {}
          });
          messageApiApp.info(`准备从 URL 添加: ${url}`);
          // fetchUserArticlesStore(); // 考虑是否真的需要在这里刷新，或者导航后由 ArticleEditPage 处理
          navigate(`/article/doi/${encodeURIComponent(url)}/edit`);
        } else {
          messageApiApp.warn('请输入有效的URL');
        }
      },
      onCancel() {},
      okText: "添加",
      cancelText: "取消",
    });
  };

  const handleNewBlankArticle = async () => {
    messageApiApp.loading({ content: '正在创建空白文章...', key: 'new-blank-article-sidebar', duration: 0 });
    try {
      const result = await createNewBlankArticleStore();
      if (result && result.newArticleUuid) {
        useStore.setState({
          article: { uuid: result.newArticleUuid, title: result.title, contentId: result.contentUuid },
          content: [],
          translated: {},
          currentTranslationControllers: new Map(),
          isTranslatingAllActive: false,
          isTranslationGloballyCancelled: false,
          indicesToTranslate: [],
          streamingChunkText: {}
        });
        messageApiApp.success({ content: '空白文章创建成功！正在跳转...', key: 'new-blank-article-sidebar', duration: 2 });
        // createNewBlankArticleStore 内部已经调用了 fetchUserArticles
        navigate(`/article/${result.newArticleUuid}/edit`);
      } else {
        throw new Error('创建文章后未收到有效的文章ID');
      }
    } catch (error) {
      messageApiApp.error({ content: `创建空白文章失败: ${error.message}`, key: 'new-blank-article-sidebar', duration: 3 });
    }
  };

  return { handleAddFromUrl, handleNewBlankArticle };
};