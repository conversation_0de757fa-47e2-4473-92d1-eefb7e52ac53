import { App } from 'antd';
import { useState, useEffect, useCallback } from 'react'; // Added useState, useEffect, useCallback
import { useNavigate } from 'react-router-dom';
import { useStore } from '../stores/index.js';
// import { getDocumentProxy, extractText, definePDFJSModule } from 'unpdf'; // getDocumentProxy and extractText are now used by pdfParser.js
import { apiBaseUrl, getOpenAIClientForCalling, corsFailedApiUrls, defaultReferenceParsePromptText, formatTextWithAI } from '../utils/index.js'; // Import defaultReferenceParsePromptText and formatTextWithAI
import { getPdfText, getPdfTextByPages, extractPdfImages } from '../utils/pdfParser.js'; // Import new parser functions
import OpenAI from 'openai';


// --- 辅助函数 ---
// 辅助函数：将 Uint8Array 转换为 Base64 Data URI
function uint8ArrayToBase64DataURI(uint8Array, mimeType) {
  let binary = '';
  const len = uint8Array.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(uint8Array[i]);
  }
  const base64 = window.btoa(binary);
  return `data:${mimeType};base64,${base64}`;
}
// 辅助函数：分割单个超长段落，优先在句号处分割
function splitSingleParagraph(paragraph, maxLength) {
  const subChunks = [];
  let currentSubChunk = paragraph;

  while (currentSubChunk.length > maxLength) {
    // 使用智能分割函数
    const smartChunk = findOptimalSentenceBreak(currentSubChunk, maxLength);
    subChunks.push(smartChunk);
    currentSubChunk = currentSubChunk.substring(smartChunk.length).trimStart();
  }

  if (currentSubChunk.length > 0) {
    subChunks.push(currentSubChunk.trim());
  }

  return subChunks.filter(sc => sc.length > 0);
}

// 辅助函数：将文本按段落分割成指定最大长度的块，优先在句号处分割
function splitTextIntoChunks(text, maxLength) {
  const finalChunks = [];
  if (!text || text.trim().length === 0) {
    return finalChunks;
  }
  // 1. Normalize paragraph breaks: split by one or more blank lines, then join with a single space.
  const paragraphs = text.split(/\n\s*\n+/).map(p => p.trim()).filter(p => p.length > 0);
  const singleContinuousText = paragraphs.join(" "); // Join with a single space

  // 2. Pass this single continuous text to splitSingleParagraph.
  if (singleContinuousText.length === 0) {
    return [];
  }
  
  // Assign to existing finalChunks, do not redeclare
  finalChunks.push(...splitSingleParagraph(singleContinuousText, maxLength));
  
  return finalChunks.filter(chunk => chunk.trim().length > 0);
}

// 新增：智能寻找最佳句号分割点的函数
function findOptimalSentenceBreak(text, maxLength) {
  // 如果文本长度在允许范围内，直接返回
  if (text.length <= maxLength) {
    return text;
  }

  // 定义更大的缓冲区域，确保能找到句号
  const bufferZone = 800;
  const searchStart = Math.max(0, maxLength - bufferZone);
  const searchEnd = Math.min(text.length, maxLength);

  // 在缓冲区域内寻找句号
  const searchText = text.substring(searchStart, searchEnd);

  // 寻找句号位置（包括中文句号），但排除小数点
  // 使用负向前瞻和负向后顾来避免匹配数字中的小数点
  const sentenceEnders = /(?<!\d)\.(?!\d)|[。!！?？]/g;
  let lastSentenceEnd = -1;
  let match;

  while ((match = sentenceEnders.exec(searchText)) !== null) {
    lastSentenceEnd = searchStart + match.index + 1; // +1 包含句号本身
  }

  // 如果在缓冲区域找到了句号，在那里分割
  if (lastSentenceEnd > 0) {
    return text.substring(0, lastSentenceEnd).trim();
  }


  // 如果没有找到句末标点，尝试在 maxLength 附近寻找次选分割点（逗号、分号、空格）
  // 从 maxLength 向前回溯，寻找最后一个合适的分割点
  let fallbackSplitPoint = -1;
  const fallbackSearchText = text.substring(0, Math.min(text.length, maxLength)); // 确保不超出文本长度

  // 1. 尝试寻找逗号或分号，但要更加谨慎
  const commaSemicolonRegex = /[,;，；]/g;
  let matchFallback;
  let lastCommaSemicolon = -1;
  while((matchFallback = commaSemicolonRegex.exec(fallbackSearchText)) !== null) {
    const commaIndex = matchFallback.index;
    // 检查逗号前后的上下文，避免在不合适的地方分割
    const beforeComma = fallbackSearchText.substring(Math.max(0, commaIndex - 20), commaIndex);
    const afterComma = fallbackSearchText.substring(commaIndex + 1, Math.min(fallbackSearchText.length, commaIndex + 21));

    // 避免在以下情况分割：
    // 1. 逗号前后都是数字（如坐标、日期等）
    // 2. 逗号前是缩写词（如 et al., Dr., etc.）
    // 3. 逗号在括号内的引用中
    const isNumberContext = /\d\s*$/.test(beforeComma) && /^\s*\d/.test(afterComma);
    const isAbbreviation = /\b(et\s+al|Dr|Mr|Mrs|Ms|Prof|vs|etc)\s*$/.test(beforeComma);
    const isInCitation = /\([^)]*$/.test(beforeComma) && !/^\s*\)/.test(afterComma);

    if (!isNumberContext && !isAbbreviation && !isInCitation) {
      lastCommaSemicolon = commaIndex + 1; // +1 包含标点本身
    }
  }
  if (lastCommaSemicolon > 0 && lastCommaSemicolon <= maxLength) { // 确保在 maxLength 内
    fallbackSplitPoint = lastCommaSemicolon;
  } else {
  }

  // 2. 如果没有找到逗号/分号，或者它们的位置不理想（例如太靠前），尝试寻找空格
  //    我们希望空格尽量靠近 maxLength
  const conditionForSpaceSearch = fallbackSplitPoint === -1 || (maxLength - fallbackSplitPoint > 50);

  if (conditionForSpaceSearch) { // 如果逗号/分号分割点离maxLength太远，宁愿找更近的空格
    let lastSpace = -1;
    // 搜索范围可以稍微扩大一点，比如 maxLength 附近
    const spaceSearchStart = Math.max(0, maxLength - 30); // 在maxLength前回溯30个字符开始找空格
    const spaceSearchText = text.substring(spaceSearchStart, Math.min(text.length, maxLength + 1)); // +1是为了确保能取到maxLength位置的字符
    
    let currentSearchIndex = spaceSearchText.length -1;
    while(currentSearchIndex >=0){
        if(spaceSearchText[currentSearchIndex] === ' '){
            lastSpace = spaceSearchStart + currentSearchIndex; // 得到在原始text中的位置
            break;
        }
        currentSearchIndex--;
    }

    if (lastSpace > 0 && lastSpace < maxLength) { // 确保在maxLength之前，并且不是开头
         // 如果找到的空格比之前找到的逗号/分号更接近maxLength（且逗号/分号不是非常接近），则优先用空格
        const conditionForUsingSpace = fallbackSplitPoint === -1 || (maxLength - lastSpace < maxLength - fallbackSplitPoint);
        if (conditionForUsingSpace) {
             fallbackSplitPoint = lastSpace + 1; // 在空格后分割
        }
    } else {
    }
  }
  
  if (fallbackSplitPoint > 0) {
    return text.substring(0, fallbackSplitPoint).trim();
  }

  // 如果以上都失败，使用原来的逻辑在maxLength处硬分割
  return text.substring(0, maxLength).trim();
}

// 新增：通过AI提取参考文献 (旧版，基于原始文本)
async function extractReferencesViaAI(rawTextFromPdf, selectedApiConfig, modelName, systemPromptForReferences, setProgressMessage, signal) {
  setProgressMessage('正在请求AI识别参考文献...');
  console.log('[extractReferencesViaAI] Attempting to extract references via AI.');

  if (signal?.aborted) {
    console.log('[extractReferencesViaAI] Operation cancelled before AI call.');
    return { error: 'Operation cancelled by user', referencesText: null, mainContentText: rawTextFromPdf };
  }

  let openai;
  try {
    const clientInfo = getOpenAIClientForCalling(selectedApiConfig, false); // Assuming direct call first
    openai = clientInfo.openai;
  } catch (instantiationError) {
    console.error('[extractReferencesViaAI] Error instantiating OpenAI client:', instantiationError);
    setProgressMessage('AI客户端初始化失败，无法提取参考文献。');
    return { error: `AI客户端初始化失败: ${instantiationError.message}`, referencesText: null, mainContentText: rawTextFromPdf };
  }

  const messages = [
    { role: 'system', content: systemPromptForReferences },
    { role: 'user', content: `以下是PDF文档的文本内容：\n---\n${rawTextFromPdf}\n---\n请识别“参考文献”部分，并返回该部分的起始和结束字符索引（0-based）。` }
  ];

  try {
    const completion = await openai.chat.completions.create({
      model: modelName,
      messages: messages,
      response_format: { type: "json_object" }, // Request JSON output
      temperature: 0.1,
    }, { signal });

    if (signal?.aborted) {
      console.log('[extractReferencesViaAI] Operation cancelled after AI call.');
      return { error: 'Operation cancelled by user', referencesText: null, mainContentText: rawTextFromPdf };
    }

    const aiResponse = completion.choices?.[0]?.message?.content;
    if (!aiResponse) {
      console.warn('[extractReferencesViaAI] AI did not return content.');
      setProgressMessage('AI未能返回参考文献位置信息。');
      return { error: 'AI did not return content', referencesText: null, mainContentText: rawTextFromPdf };
    }

    console.log('[extractReferencesViaAI] Raw AI response for references:', aiResponse);
    // Log a snippet of the rawTextFromPdf for context
    const rawTextSnippetLength = 500;
    console.log(`[extractReferencesViaAI] Snippet of rawTextFromPdf (first ${rawTextSnippetLength} chars):`, rawTextFromPdf.substring(0, rawTextSnippetLength));
    console.log(`[extractReferencesViaAI] Snippet of rawTextFromPdf (last ${rawTextSnippetLength} chars):`, rawTextFromPdf.substring(Math.max(0, rawTextFromPdf.length - rawTextSnippetLength)));


    let parsedResponse;
    try {
      parsedResponse = parseAIResponseAsJSON(aiResponse, ['references_start_index', 'references_end_index']);
      console.log('[extractReferencesViaAI] Successfully parsed AI response:', parsedResponse);
    } catch (e) {
      console.error('[extractReferencesViaAI] Failed to parse AI response as JSON:', e, 'Response:', aiResponse);
      setProgressMessage('AI返回的参考文献位置信息格式错误。');
      return { error: 'Failed to parse AI JSON response', referencesText: null, mainContentText: rawTextFromPdf };
    }

    const { references_start_index, references_end_index } = parsedResponse;
    console.log('[extractReferencesViaAI] Parsed AI response:', parsedResponse);

    if (typeof references_start_index !== 'number' || typeof references_end_index !== 'number' || references_start_index === -1 || references_end_index === -1) {
      console.log('[extractReferencesViaAI] AI did not find references or returned invalid indices (e.g., -1).');
      setProgressMessage('AI未能定位参考文献。');
      return { referencesText: null, mainContentText: rawTextFromPdf }; // No error, just not found
    }

    if (references_start_index >= references_end_index || references_start_index < 0 || references_end_index > rawTextFromPdf.length) {
      console.warn('[extractReferencesViaAI] AI returned invalid range for references:', {references_start_index, references_end_index, rawTextLength: rawTextFromPdf.length });
      setProgressMessage('AI返回的参考文献位置范围无效。');
      return { error: 'AI returned invalid range for references', referencesText: null, mainContentText: rawTextFromPdf };
    }

    const referencesText = rawTextFromPdf.substring(references_start_index, references_end_index).trim();
    const part1 = rawTextFromPdf.substring(0, references_start_index);
    const part2 = rawTextFromPdf.substring(references_end_index);
    const mainContentText = (part1 + part2).trim();

    console.log(`[extractReferencesViaAI] Successfully extracted references. Start: ${references_start_index}, End: ${references_end_index}, Length: ${referencesText.length}`);
    console.log('[extractReferencesViaAI] Extracted References Text (first 300 chars):', referencesText.substring(0, 300));
    console.log('[extractReferencesViaAI] Main Content Text (snippet around split start - 150 chars before and after):', rawTextFromPdf.substring(Math.max(0, references_start_index - 150), Math.min(rawTextFromPdf.length, references_start_index + 150)));
    console.log('[extractReferencesViaAI] Main Content Text (snippet around split end - 150 chars before and after):', rawTextFromPdf.substring(Math.max(0, references_end_index - 150), Math.min(rawTextFromPdf.length, references_end_index + 150)));


    setProgressMessage('AI成功识别参考文献。');
    return { referencesText, mainContentText };

  } catch (error) {
    if (signal?.aborted || error.name === 'AbortError') {
      console.log('[extractReferencesViaAI] AI call aborted:', error.message);
      setProgressMessage('参考文献提取已取消。');
      return { error: 'Operation cancelled by user', referencesText: null, mainContentText: rawTextFromPdf };
    }
    console.error('[extractReferencesViaAI] Error calling AI for references:', error);
    setProgressMessage(`提取参考文献时AI调用失败: ${error.message}`);
    return { error: `AI call failed: ${error.message}`, referencesText: null, mainContentText: rawTextFromPdf };
  }
}

// 辅助函数：清理AI响应，提取纯JSON
function cleanAIResponseForJSON(aiResponse) {
  if (!aiResponse || typeof aiResponse !== 'string') {
    return aiResponse;
  }

  let cleaned = aiResponse.trim();

  // 1. 移除markdown代码块标记
  cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, '');
  cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');

  // 2. 尝试提取JSON对象 - 寻找第一个 { 到最后一个 }
  const firstBrace = cleaned.indexOf('{');
  const lastBrace = cleaned.lastIndexOf('}');

  if (firstBrace !== -1 && lastBrace !== -1 && firstBrace <= lastBrace) {
    cleaned = cleaned.substring(firstBrace, lastBrace + 1);
  }

  // 3. 移除常见的AI响应前缀
  const commonPrefixes = [
    /^Here\s+is\s+the\s+JSON.*?:/i,
    /^The\s+JSON\s+response.*?:/i,
    /^Based\s+on.*?:/i,
    /^According\s+to.*?:/i,
    /^以下是.*?：/,
    /^根据.*?：/,
    /^JSON\s*格式.*?：/i,
    /^返回的?\s*JSON.*?：/i
  ];

  // 应用前缀清理
  for (const prefix of commonPrefixes) {
    cleaned = cleaned.replace(prefix, '');
  }

  // 重新寻找JSON边界（因为可能有变化）
  const newFirstBrace = cleaned.indexOf('{');
  const newLastBrace = cleaned.lastIndexOf('}');

  if (newFirstBrace !== -1 && newLastBrace !== -1 && newFirstBrace <= newLastBrace) {
    cleaned = cleaned.substring(newFirstBrace, newLastBrace + 1);
  }

  // 4. 最终清理
  cleaned = cleaned.trim();

  return cleaned;
}

// 新增：强健的JSON解析函数，支持多种格式的AI响应
function parseAIResponseAsJSON(aiResponse, expectedKeys = []) {
  if (!aiResponse || typeof aiResponse !== 'string') {
    throw new Error('AI响应为空或格式无效');
  }

  // 首先尝试清理响应
  const cleanedResponse = cleanAIResponseForJSON(aiResponse);

  // 尝试多种解析策略
  const parseStrategies = [
    // 策略1：直接解析清理后的响应
    () => JSON.parse(cleanedResponse),

    // 策略2：尝试提取第一个完整的JSON对象
    () => {
      const jsonMatch = cleanedResponse.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error('未找到有效的JSON对象');
    },

    // 策略3：尝试修复常见的JSON格式问题
    () => {
      let fixed = cleanedResponse
        .replace(/,\s*}/g, '}')  // 移除尾随逗号
        .replace(/,\s*]/g, ']')  // 移除数组尾随逗号
        .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // 为未引用的键添加引号
        .replace(/:\s*([^",\[\]{}]+)(?=[,}])/g, ': "$1"');  // 为未引用的值添加引号（简单情况）
      return JSON.parse(fixed);
    },

    // 策略4：尝试提取多行JSON（处理换行问题）
    () => {
      const lines = cleanedResponse.split('\n').map(line => line.trim()).filter(line => line);
      const jsonLines = [];
      let inJson = false;

      for (const line of lines) {
        if (line.includes('{')) inJson = true;
        if (inJson) jsonLines.push(line);
        if (line.includes('}')) break;
      }

      return JSON.parse(jsonLines.join(''));
    }
  ];

  let lastError = null;

  // 依次尝试每种策略
  for (let i = 0; i < parseStrategies.length; i++) {
    try {
      const result = parseStrategies[i]();

      // 验证解析结果是否包含期望的键
      if (expectedKeys.length > 0) {
        const missingKeys = expectedKeys.filter(key => !(key in result));
        if (missingKeys.length > 0) {
          console.warn(`[parseAIResponseAsJSON] 策略${i + 1}解析成功但缺少键: ${missingKeys.join(', ')}`);
          continue; // 尝试下一个策略
        }
      }

      console.log(`[parseAIResponseAsJSON] 策略${i + 1}解析成功`);
      return result;

    } catch (error) {
      lastError = error;
      console.warn(`[parseAIResponseAsJSON] 策略${i + 1}失败:`, error.message);
    }
  }

  // 所有策略都失败了
  console.error('[parseAIResponseAsJSON] 所有解析策略都失败了');
  console.error('[parseAIResponseAsJSON] 原始响应:', aiResponse);
  console.error('[parseAIResponseAsJSON] 清理后响应:', cleanedResponse);
  throw new Error(`JSON解析失败，已尝试${parseStrategies.length}种策略。最后错误: ${lastError?.message || '未知错误'}`);
}

// 新增：通过AI从结构化内容中提取参考文献
async function extractReferencesFromStructuredContentAI(
  allEnhancedContentJson,
  selectedApiConfig,
  modelName,
  setProgressMessage,
  signal
) {
  const MAX_RETRIES = 1; // 最多重试1次
  let currentAttempt = 0;

  while (currentAttempt <= MAX_RETRIES) {
    const isRetry = currentAttempt > 0;
    const attemptMessage = isRetry ? `正在重试AI参考文献识别 (第${currentAttempt + 1}次尝试)...` : '正在请求AI从结构化内容中识别参考文献...';
    setProgressMessage(attemptMessage);

    console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}/${MAX_RETRIES + 1} to extract references from structured JSON.`);

    if (signal?.aborted) {
      console.log('[extractReferencesFromStructuredContentAI] Operation cancelled before AI call.');
      return { error: 'Operation cancelled by user', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
    }

    if (!allEnhancedContentJson || allEnhancedContentJson.length === 0) {
      console.log('[extractReferencesFromStructuredContentAI] No structured content provided.');
      return { extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
    }

    let openai;
    try {
      const clientInfo = getOpenAIClientForCalling(selectedApiConfig, false);
      openai = clientInfo.openai;
    } catch (instantiationError) {
      console.error('[extractReferencesFromStructuredContentAI] Error instantiating OpenAI client:', instantiationError);
      setProgressMessage('AI客户端初始化失败，无法提取参考文献。');
      return { error: `AI客户端初始化失败: ${instantiationError.message}`, extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
    }

    // 为AI准备简化的JSON字符串，只包含id, tag和children的文本内容
    // 同时，为了减少token，只发送文本内容的前N个字符
    const MAX_CHILDREN_TEXT_LENGTH = 100; // 每个对象中文本内容的最大长度
    const simplifiedJsonForAI = allEnhancedContentJson.map((item, index) => {
      let childrenText = '';
      if (typeof item.children === 'string') {
        childrenText = item.children.substring(0, MAX_CHILDREN_TEXT_LENGTH);
      } else if (Array.isArray(item.children)) { // 处理嵌套结构（简单处理）
        childrenText = item.children
          .filter(child => typeof child.text === 'string')
          .map(child => child.text)
          .join(' ')
          .substring(0, MAX_CHILDREN_TEXT_LENGTH);
      }
      return { id: index, tag: item.tag, children: childrenText }; // 使用原始索引作为id
    });

    const jsonStringForAI = JSON.stringify(simplifiedJsonForAI);
    // 考虑Token限制，如果jsonStringForAI过长，可能需要进一步截断或分块（暂不实现）
    console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Simplified JSON string for AI length: ${jsonStringForAI.length}. Sample (first 500 chars): ${jsonStringForAI.substring(0, 500)}`);

    const systemPrompt = `You are an expert in academic paper analysis. You will be given a JSON array representing structured content from a document. Each object in the array has an "id" (its original index in the full array), a "tag" (e.g., "p", "h1", "li"), and "children" (text content snippet). Identify the block of objects that constitutes the "References" or "参考文献" section. Return a JSON object with two keys: "references_start_id" and "references_end_id", representing the "id" (original index) of the first and last object in the input JSON array that belongs to this section. If no such section is found, return {"references_start_id": -1, "references_end_id": -1}. Do not include any explanatory text outside the JSON object.`;
    console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: System prompt for AI: ${systemPrompt}`);
    console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: User prompt for AI (content part - first 200 chars of JSON string): ${jsonStringForAI.substring(0,200)}...`);

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: `Here is the structured document content as a JSON array:\n---\n${jsonStringForAI}\n---\nPlease identify the "References" section and return the start and end IDs.` }
    ];

    try {
      const completion = await openai.chat.completions.create({
        model: modelName,
        messages: messages,
        response_format: { type: "json_object" },
        temperature: 0.1,
      }, { signal });

      if (signal?.aborted) {
        console.log('[extractReferencesFromStructuredContentAI] Operation cancelled after AI call.');
        return { error: 'Operation cancelled by user', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }

      const aiResponse = completion.choices?.[0]?.message?.content;
      if (!aiResponse) {
        console.warn(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: AI did not return content.`);
        if (currentAttempt < MAX_RETRIES) {
          console.log(`[extractReferencesFromStructuredContentAI] Retrying due to empty AI response...`);
          currentAttempt++;
          continue; // 重试
        }
        setProgressMessage('AI未能返回参考文献位置信息。');
        return { error: 'AI did not return content', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }

      console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Raw AI response:`, aiResponse);

      // 使用强健的JSON解析函数
      let parsedResponse;
      try {
        parsedResponse = parseAIResponseAsJSON(aiResponse, ['references_start_id', 'references_end_id']);
        console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Successfully parsed AI response:`, parsedResponse);
      } catch (e) {
        console.error(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Failed to parse AI response as JSON:`, e, 'Original Response:', aiResponse);
        if (currentAttempt < MAX_RETRIES) {
          console.log(`[extractReferencesFromStructuredContentAI] Retrying due to JSON parse error...`);
          currentAttempt++;
          continue; // 重试
        }
        setProgressMessage('AI返回的参考文献位置信息格式错误。');
        return { error: 'Failed to parse AI JSON response', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }

      const { references_start_id, references_end_id } = parsedResponse;
      console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Parsed AI response:`, parsedResponse);

      if (typeof references_start_id !== 'number' || typeof references_end_id !== 'number' || references_start_id === -1 || references_end_id === -1) {
        console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: AI did not find references or returned invalid IDs.`);
        if (currentAttempt < MAX_RETRIES) {
          console.log(`[extractReferencesFromStructuredContentAI] Retrying due to invalid IDs...`);
          currentAttempt++;
          continue; // 重试
        }
        setProgressMessage('AI未能从结构化内容中定位参考文献。');
        return { extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }

      if (references_start_id < 0 || references_end_id >= allEnhancedContentJson.length || references_start_id > references_end_id) {
        console.warn(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: AI returned invalid ID range for references:`, { references_start_id, references_end_id, arrayLength: allEnhancedContentJson.length });
        if (currentAttempt < MAX_RETRIES) {
          console.log(`[extractReferencesFromStructuredContentAI] Retrying due to invalid ID range...`);
          currentAttempt++;
          continue; // 重试
        }
        setProgressMessage('AI返回的参考文献ID范围无效。');
        return { error: 'AI returned invalid ID range for references', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }

      // --- 后处理逻辑：优化参考文献结束点 ---
      let refinedEndId = references_end_id;
      const NON_REFERENCE_KEYWORDS = [
        "this preprint research paper has not been peer reviewed",
        "patient characteristics",
        "author contributions",
        "funding",
        "acknowledgments",
        "acknowledgements",
        "supplementary material",
        "data availability",
        "competing interests",
        "conflict of interest",
        "ethical approval",
        "figure legends",
        "table captions"
      ];

      console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Initial AI end_id: ${references_end_id}. Starting post-processing to refine end_id.`);

      for (let i = references_start_id; i <= references_end_id; i++) {
        const item = allEnhancedContentJson[i];
        if (!item) continue;

        if (item.tag === 'table') {
          console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Post-processing: Found 'table' tag at ID ${i}. Refining end_id to ${i - 1}.`);
          refinedEndId = i - 1;
          break;
        }
        if (typeof item.children === 'string') {
          const itemTextLower = item.children.toLowerCase();
          for (const keyword of NON_REFERENCE_KEYWORDS) {
            if (itemTextLower.includes(keyword)) {
              console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Post-processing: Found keyword "${keyword}" at ID ${i}. Refining end_id to ${i - 1}.`);
              refinedEndId = i - 1;
              break;
            }
          }
          if (refinedEndId < i) break;
        }
      }

      if (refinedEndId < references_start_id) {
        console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Post-processing determined no valid reference items after AI's start_id (${references_start_id}). Original AI end_id was ${references_end_id}. Refined end_id became ${refinedEndId}. Returning no references.`);
        return { extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }
      console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Final refinedEndId after post-processing: ${refinedEndId}`);
      // --- 结束 后处理逻辑 ---

      const referenceObjects = allEnhancedContentJson.slice(references_start_id, refinedEndId + 1);
      const extractedReferencesText = referenceObjects.map(item => {
        if (typeof item.children === 'string') return item.children;
        if (Array.isArray(item.children)) {
          return item.children.filter(c => typeof c.text === 'string').map(c => c.text).join('');
        }
        return '';
      }).join('\n').trim();

      // 主内容只包含AI识别的参考文献开始点之前的部分
      const updatedMainContentJson = allEnhancedContentJson.slice(0, references_start_id);

      console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Successfully processed. AI start_id: ${references_start_id}, Refined end_id for refs: ${refinedEndId}.`);
      console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Original total items: ${allEnhancedContentJson.length}, Extracted reference items: ${referenceObjects.length}, Final main content items: ${updatedMainContentJson.length}`);

      if (referenceObjects.length === 0 && extractedReferencesText.length === 0) {
          console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: No reference items found after post-processing, returning null for references text.`);
          setProgressMessage('未能从结构化内容中精确定位参考文献。');
          return { extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }

      setProgressMessage('AI成功从结构化内容中识别并优化了参考文献范围。');
      return { extractedReferencesText, updatedAllEnhancedContentJson: updatedMainContentJson };

    } catch (error) {
      if (signal?.aborted || error.name === 'AbortError') {
        console.log(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: AI call aborted:`, error.message);
        setProgressMessage('参考文献提取已取消。');
        return { error: 'Operation cancelled by user', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
      }
      console.error(`[extractReferencesFromStructuredContentAI] Attempt ${currentAttempt + 1}: Error calling AI:`, error);
      if (currentAttempt < MAX_RETRIES) {
        console.log(`[extractReferencesFromStructuredContentAI] Retrying due to AI call error...`);
        currentAttempt++;
        continue; // 重试
      }
      setProgressMessage(`从结构化内容提取参考文献时AI调用失败: ${error.message}`);
      return { error: `AI call failed: ${error.message}`, extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
    }
  }

  // 如果所有重试都失败了，返回错误
  console.error(`[extractReferencesFromStructuredContentAI] All ${MAX_RETRIES + 1} attempts failed.`);
  setProgressMessage('AI参考文献识别重试次数已用完，提取失败。');
  return { error: 'All retry attempts failed', extractedReferencesText: null, updatedAllEnhancedContentJson: allEnhancedContentJson };
}

// 新增：通过AI格式化参考文献文本
async function formatReferencesViaAI(
  rawReferencesText,
  selectedApiConfig,
  modelName,
  systemPromptForFormatting,
  signal,
  setProgressMessage // This can still be used for high-level status updates
) {
  if (!rawReferencesText || rawReferencesText.trim() === '') {
    console.log('[formatReferencesViaAI] No raw references text provided to format.');
    return { formattedText: null, error: null };
  }

  setProgressMessage('正在请求AI格式化参考文献...'); // Initial message
  console.log('[formatReferencesViaAI] Attempting to format references via shared formatTextWithAI utility.');

  if (signal?.aborted) { // Check before calling
    console.log('[formatReferencesViaAI] Operation cancelled before calling shared utility.');
    setProgressMessage('参考文献格式化已取消。');
    return { formattedText: null, error: 'Operation cancelled by user' };
  }

  // Call the shared utility function
  // formatTextWithAI handles its own detailed progress messages, CORS retries, and signal abortion.
  // concurrencyLevel is handled internally by formatTextWithAI if text is large.
  // For a single block of reference text, it's not directly applicable here.
  const result = await formatTextWithAI(
    rawReferencesText,          // textToProcess
    systemPromptForFormatting,  // systemPrompt
    selectedApiConfig,          // selectedApiConfig
    modelName,                  // modelName
    signal,                     // signal (AbortSignal)
    useStore.getState().user?.config?.concurrencyLevel || 1 // Pass user's concurrency limit, default to 1
    // disableChunking will use its default (false)
  );

  // formatTextWithAI returns { success: boolean, data?: string, error?: string }
  if (!result.success) { // Check success flag
    console.error('[formatReferencesViaAI] Shared utility failed to format references or was cancelled:', result.error);
    const finalErrorMessage = result.error === 'Operation cancelled by user' ? '参考文献格式化已取消。' : `参考文献格式化失败: ${result.error || '未知错误'}`;
    setProgressMessage(finalErrorMessage);
    return { formattedText: null, error: result.error || '未知错误' };
  }

  // If success is true, but no data (should be rare if success is true)
  if (result.success && !result.data) {
      console.warn('[formatReferencesViaAI] Shared utility returned success but no formatted text.');
      setProgressMessage('AI未能返回格式化后的参考文献 (来自共享工具，但标记为成功)。');
      return { formattedText: null, error: 'AI did not return formatted content despite success flag' };
  }

  console.log('[formatReferencesViaAI] Successfully formatted references using shared utility.');
  setProgressMessage('AI成功格式化参考文献。'); // Final success message
  return { formattedText: result.data, error: null }; // Use result.data
}

// 辅助函数：并发处理任务
async function processInParallelWithProgress(items, asyncOperation, concurrencyLevel, setProgressMessage, signal) {
  const results = new Array(items.length);
  const queue = [...items.entries()];
  let activePromisesCount = 0;
  let completedCount = 0;
  const totalItems = items.length;

  if (totalItems === 0) {
    return Promise.resolve([]);
  }

  const msgPrepare = `准备处理 ${totalItems} 个文本块...`;
  // console.log('[usePdfImport][processInParallelWithProgress] Calling setProgressMessage:', msgPrepare); // Already in a helper, might be too verbose
  setProgressMessage(msgPrepare);

  return new Promise((resolve, reject) => {
    function runNextTask() {
      if (signal?.aborted) {
        // Do not reject here, allow ongoing operations to finish and collect results
        // The main handler will check for signal.aborted after processInParallelWithProgress resolves
        // Ensure already started tasks can complete, but no new ones start.
        // If all tasks are done, resolve.
        if (completedCount === totalItems && activePromisesCount === 0) {
          resolve(results);
        }
        return;
      }

      if (completedCount === totalItems) {
        if (activePromisesCount === 0) {
          resolve(results);
        }
        return;
      }

      while (activePromisesCount < concurrencyLevel && queue.length > 0) {
        if (signal?.aborted) { // Double check before starting a new task
          if (completedCount === totalItems && activePromisesCount === 0) {
            resolve(results);
          }
          return;
        }

        // 检查是否用户选择跳过AI解析
        const currentShouldSkip = useStore.getState().shouldSkipAiParsing;
        console.log('while循环中检查跳过状态:', currentShouldSkip, '队列剩余:', queue.length, '活跃任务:', activePromisesCount, '已完成:', completedCount);
        if (currentShouldSkip) {
          console.log('检测到用户选择跳过AI解析，停止启动新任务');
console.log('跳过AI解析时，活跃任务数:', activePromisesCount, 'Signal aborted:', signal?.aborted);
          // 用户选择跳过，停止启动新任务，但让已经开始的任务完成
          if (completedCount === totalItems && activePromisesCount === 0) {
            console.log('所有任务已完成，解析processInParallelWithProgress');
            resolve(results);
          }
          return;
        }

        const [index, item] = queue.shift();
        activePromisesCount++;
        // Initial message for starting a task
        if (!signal?.aborted) {
            const msgAiStart = `AI处理中: ${completedCount + 1}/${totalItems} 开始 (并发: ${activePromisesCount})`;
            // console.log('[usePdfImport][processInParallelWithProgress] Calling setProgressMessage:', msgAiStart);
            setProgressMessage(msgAiStart);
        }

        asyncOperation(item, index, signal) // Pass signal to asyncOperation
          .then(result => { // asyncOperation now resolves with success/failure object
            results[index] = result;
            if (!result.success) {
              // This message is now handled by callAiForChunk for final failure
              // setProgressMessage(`AI 处理：块 ${index + 1} 解析失败。`);
              console.warn(`[processInParallelWithProgress] Chunk ${index + 1} processing failed after retries:`, result.error, result.details);
            }
          })
          .catch(error => { // This catch should ideally not be hit if callAiForChunk handles its errors
            console.error(`[processInParallelWithProgress] UNEXPECTED ERROR for chunk ${index + 1}:`, error);
            results[index] = { success: false, error: "UNEXPECTED_PROCESSING_ERROR", originalData: item, index: index, details: error.message };
            // Do not reject the whole process here.
          })
          .finally(() => {
            activePromisesCount--;
            completedCount++;
            console.log(`[finally] 任务完成: 活跃任务数=${activePromisesCount}, 已完成=${completedCount}/${totalItems}`);
            if (!signal?.aborted) {
              const msgAiComplete = `AI处理: ${completedCount}/${totalItems} 完成 (并发: ${activePromisesCount})`;
              // console.log('[usePdfImport][processInParallelWithProgress] Calling setProgressMessage:', msgAiComplete);
              setProgressMessage(msgAiComplete);
            }
            if (completedCount === totalItems) {
              console.log(`[finally] 所有任务已完成，活跃任务数=${activePromisesCount}`);
              if (activePromisesCount === 0) {
                console.log('[finally] 解析processInParallelWithProgress - 所有任务完成');
                resolve(results);
              }
            } else if (!signal?.aborted) {
              // 在启动下一个任务前检查是否用户选择跳过
              const shouldSkipNow = useStore.getState().shouldSkipAiParsing;
              console.log('finally块中检查跳过状态:', shouldSkipNow, '准备启动下一个任务, 活跃任务数:', activePromisesCount);
              if (!shouldSkipNow) {
                runNextTask();
              } else {
                console.log('用户选择跳过，不启动下一个任务');
                // 如果没有活跃任务了，直接resolve
                if (activePromisesCount === 0) {
                  console.log('没有活跃任务，解析processInParallelWithProgress');
                  resolve(results);
                }
              }
            } else if (signal?.aborted && activePromisesCount === 0 && completedCount === totalItems) {
              // If aborted and all active tasks finished
              console.log('[finally] 解析processInParallelWithProgress - 任务被中止且所有任务完成');
              resolve(results);
            }
          });
      }
    }

    const initialBatchSize = Math.min(concurrencyLevel, totalItems);
    for (let i = 0; i < initialBatchSize; i++) {
      if (signal?.aborted) {
          break; // Stop initiating new tasks
      }
      runNextTask();
    }
    // If initialBatchSize is 0 (e.g. totalItems is 0, though handled above) or if loop breaks early due to abort
    if (initialBatchSize === 0 || (signal?.aborted && activePromisesCount === 0 && completedCount === totalItems)) {
        resolve(results); // Ensure promise resolves if no tasks were started or all aborted quickly
    }
  });
}
// 新增：创建简单段落结构的函数（跳过AI解析时使用）
function createSimpleParagraphStructure(textChunks) {
  const simplifiedContent = [];

  textChunks.forEach((chunk) => {
    // 将每个文本块按段落分割
    const paragraphs = chunk.split(/\n\s*\n+/).map(p => p.trim()).filter(p => p.length > 0);

    paragraphs.forEach(paragraph => {
      if (paragraph.length > 0) {
        simplifiedContent.push({
          tag: 'p',
          children: paragraph
        });
      }
    });
  });

  return simplifiedContent;
}

// --- 辅助函数结束 ---

// 新增：数据分块函数
const segmentPayload = (jsonData, chunkSizeMB) => {
  const MAX_CHUNK_SIZE_BYTES = chunkSizeMB * 1024 * 1024;
  const chunks = [];
  let currentChunk = [];
  let currentChunkSize = 0;

  jsonData.forEach(item => {
    const itemString = JSON.stringify(item);
    const itemSize = itemString.length;

    if (itemSize > MAX_CHUNK_SIZE_BYTES) {
      if (currentChunk.length > 0) {
        chunks.push(currentChunk);
        currentChunk = [];
        currentChunkSize = 0;
      }
      chunks.push([item]);
      // console.log(`[segmentPayload] Large item (size: ${itemSize} bytes) forms its own chunk.`);
      return;
    }

    if (currentChunkSize + itemSize > MAX_CHUNK_SIZE_BYTES && currentChunk.length > 0) {
      chunks.push(currentChunk);
      currentChunk = [item];
      currentChunkSize = itemSize;
    } else {
      currentChunk.push(item);
      currentChunkSize += itemSize;
    }
  });

  if (currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  // console.log(`[segmentPayload] Segmented data into ${chunks.length} chunks for target size ${chunkSizeMB}MB.`);
  // chunks.forEach((chunk, index) => {
  //   console.log(`[segmentPayload] Chunk ${index + 1} size: ${JSON.stringify(chunk).length} bytes, items: ${chunk.length}`);
  // });
  return chunks;
};

// 新增：分块上传函数
const uploadInChunks = async (uploadSessionId, dataChunks, fileName, signal, setProgressMessage) => {
  const totalChunks = dataChunks.length;
  const MAX_RETRIES_PER_CHUNK = 3;
  const RETRY_DELAY_MS = 2000;

  if (totalChunks === 0) {
    // console.log('[uploadInChunks] No data chunks to upload.');
    return;
  }

  for (let i = 0; i < totalChunks; i++) {
    const chunkData = dataChunks[i];
    let attempts = 0;

    while (attempts < MAX_RETRIES_PER_CHUNK) {
      attempts++;
      if (signal?.aborted) {
        // console.log(`[uploadInChunks] Upload cancelled by user before processing chunk ${i + 1}.`);
        throw new Error('Operation cancelled by user.');
      }

      const msgUploadAttempt = `数据提交中：块 ${i + 1}/${totalChunks} (尝试 ${attempts}/${MAX_RETRIES_PER_CHUNK})`;
      // console.log('[usePdfImport][uploadInChunks] Calling setProgressMessage:', msgUploadAttempt);
      setProgressMessage(msgUploadAttempt);
      // console.log(`[uploadInChunks] Attempting to upload chunk ${i + 1}/${totalChunks}, attempt ${attempts}, size: ${JSON.stringify(chunkData).length} bytes`);

      try {
        const response = await fetch(`${apiBaseUrl}api/import-pdf/chunked`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            uploadSessionId,
            chunkIndex: i,
            totalChunks,
            fileName,
            data: chunkData
          }),
          signal,
        });

        if (signal?.aborted) {
          // console.log(`[uploadInChunks] Upload cancelled by user immediately after fetch for chunk ${i + 1}.`);
          throw new Error('Operation cancelled by user.');
        }

        if (response.ok) {
          // try {
          //   const responseData = await response.json();
          //   console.log(`[uploadInChunks] Chunk ${i + 1}/${totalChunks} uploaded successfully. Response:`, responseData);
          // } catch (e) {
          //   console.log(`[uploadInChunks] Chunk ${i + 1}/${totalChunks} uploaded successfully. No JSON response or parse error.`);
          // }
          const msgUploadSuccess = `数据提交：块 ${i + 1}/${totalChunks} 完成`;
          // console.log('[usePdfImport][uploadInChunks] Calling setProgressMessage:', msgUploadSuccess);
          setProgressMessage(msgUploadSuccess);
          break;
        } else {
          const errorStatus = response.status;
          const errorText = await response.text().catch(() => "无法读取错误响应体");
          console.error(`[uploadInChunks] Failed to upload chunk ${i + 1}/${totalChunks}, attempt ${attempts}. Status: ${errorStatus}, Body: ${errorText}`);
          if (attempts >= MAX_RETRIES_PER_CHUNK) {
            const finalErrorMessage = `数据块 ${i + 1}/${totalChunks} 提交失败 (尝试 ${attempts} 次)。服务器响应: ${errorStatus} - ${errorText.substring(0,150)}`;
            // console.log('[usePdfImport][uploadInChunks] Calling setProgressMessage (final error):', finalErrorMessage);
            setProgressMessage(finalErrorMessage);
            throw new Error(finalErrorMessage);
          }
          const msgUploadRetry = `数据提交：块 ${i + 1}/${totalChunks} 失败，${RETRY_DELAY_MS/1000}秒后重试 (尝试 ${attempts}/${MAX_RETRIES_PER_CHUNK})`;
          // console.log('[usePdfImport][uploadInChunks] Calling setProgressMessage:', msgUploadRetry);
          setProgressMessage(msgUploadRetry);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
        }
      } catch (error) {
        if (error.name === 'AbortError' || signal?.aborted) {
          // console.log(`[uploadInChunks] Upload aborted for chunk ${i + 1} during attempt ${attempts}. Error: ${error.message}`);
          throw new Error('Operation cancelled by user.');
        }
        console.error(`[uploadInChunks] Network or unexpected error uploading chunk ${i + 1}/${totalChunks}, attempt ${attempts}:`, error); // Keep this error
        if (attempts >= MAX_RETRIES_PER_CHUNK) {
          const finalErrorMessage = `数据块 ${i + 1}/${totalChunks} 提交时发生网络或意外错误 (尝试 ${attempts} 次): ${error.message}`;
          // console.log('[usePdfImport][uploadInChunks] Calling setProgressMessage (final network error):', finalErrorMessage);
          setProgressMessage(finalErrorMessage);
          throw new Error(finalErrorMessage);
        }
        const msgUploadNetErrRetry = `数据提交：块 ${i + 1}/${totalChunks} 发生错误，${RETRY_DELAY_MS/1000}秒后重试 (尝试 ${attempts}/${MAX_RETRIES_PER_CHUNK})`;
        // console.log('[usePdfImport][uploadInChunks] Calling setProgressMessage:', msgUploadNetErrRetry);
        setProgressMessage(msgUploadNetErrRetry);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
      }
    }
  }
  // console.log('[uploadInChunks] All chunks processed successfully.');
};


export const usePdfImport = (fileInputRef, messageApiApp) => {
  // console.log('%c[usePdfImport] Hook execution START', 'color: green; font-weight: bold;', { fileInputRef: !!fileInputRef, messageApiApp: !!messageApiApp });
  const navigate = useNavigate();
  const storeActions = useStore.getState(); // Get all actions once
  // Get specific actions and states from store for easier use and reactivity if needed via selectors
  const {
    setCanRetryFinalizePdf,
    setPdfFinalizeErrorDetails,
    triggerPdfImportRetry: triggerPdfImportRetryFromStore, // Renaming to avoid conflict
    abortPdfImportProcess, // New action from store
    setCurrentImportController: storeSetCurrentImportController, // Alias to avoid conflict
    clearCurrentImportController: storeClearCurrentImportController,
    setIsImportingPdf: storeSetIsImportingPdf,
    setImportProgressMessage: storeSetImportProgressMessage
  } = useStore.getState();

  // This local state is still useful for managing the retry *process* within the hook
  const [internalFinalizeRetryInfo, setInternalFinalizeRetryInfo] = useState(null);
  const [isRetryingFinalize, setIsRetryingFinalize] = useState(false);
  // console.log('[usePdfImport] Initial state (or current upon re-render):', { internalFinalizeRetryInfo, isRetryingFinalize });

  // Helper function for full cleanup
  const performFullImportCleanup = () => {
    // console.log('%c[usePdfImport] performFullImportCleanup called', 'color: red; font-weight: bold;');
    // console.log('[usePdfImport] Calling storeSetIsImportingPdf with (in performFullImportCleanup):', false);
    storeSetIsImportingPdf(false);
    // console.log('[usePdfImport] Calling storeClearCurrentImportController (in performFullImportCleanup)');
    storeClearCurrentImportController(); // This should also clear canRetryFinalizePdf and pdfFinalizeErrorDetails in stores/index.js

    // Clear local hook state as well
    // console.log('[usePdfImport] Calling setInternalFinalizeRetryInfo with (in performFullImportCleanup):', null);
    setInternalFinalizeRetryInfo(null);
    // console.log('[usePdfImport] Calling setIsRetryingFinalize with (in performFullImportCleanup):', false);
    setIsRetryingFinalize(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    // console.log('[PDFImport] Full import cleanup performed.');
  };

  // useCallback for retryFinalizeUpload to stabilize its reference for useEffect dependency
  const retryFinalizeUpload = useCallback(async () => {
    if (!internalFinalizeRetryInfo || isRetryingFinalize) {
      // console.log('[usePdfImport-Retry] Conditions not met for retry:', { internalFinalizeRetryInfo, isRetryingFinalize });
      return;
    }

    // Actions are already destructured at the top of the hook
    // const {
    //   setIsImportingPdf, // storeSetIsImportingPdf
    //   setImportProgressMessage, // storeSetImportProgressMessage
    //   // clearCurrentImportController, // storeClearCurrentImportController
    //   fetchUserArticles
    // } = storeActions;

    const importController = useStore.getState().currentImportController;
    const importControllerSignal = importController?.signal;

    // console.log('[usePdfImport] Calling setIsRetryingFinalize with (retryFinalizeUpload, start):', true);
    setIsRetryingFinalize(true);
    // console.log('[usePdfImport] Calling setCanRetryFinalizePdf(false) from retryFinalizeUpload start');
    setCanRetryFinalizePdf(false); // Disable retry button during attempt

    const msgRetryConfirm = '正在重试最终确认...';
    // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (retryFinalizeUpload, start):', msgRetryConfirm);
    storeSetImportProgressMessage(msgRetryConfirm);
    // console.log('[PDFImport-Retry] Attempting to retry finalize-upload.');

    try {
      // Destructure all necessary fields from internalFinalizeRetryInfo, including allEnhancedContentJson
      const { uploadSessionId, fileName, totalExpectedChunks, allEnhancedContentJson: allEnhancedContentJsonFromRetry } = internalFinalizeRetryInfo;

      const finalizeSubmitResponse = await fetch(`${apiBaseUrl}api/import-pdf/finalize-upload`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          uploadSessionId,
          fileName,
          totalExpectedChunks
        }),
        signal: importControllerSignal,
      });

      if (importControllerSignal?.aborted) {
        setIsRetryingFinalize(false);
        // Message and cleanup handled by main abort logic
        return;
      }

      if (!finalizeSubmitResponse.ok) {
        const errorData = await finalizeSubmitResponse.json().catch(() => ({ error: '重试提交后台处理请求失败，无法解析错误信息。' }));
        const errorMessage = errorData.error || `重试提交后台处理请求错误: ${finalizeSubmitResponse.status}`;

        setInternalFinalizeRetryInfo(prev => ({ ...prev, message: errorMessage })); // Keep other retry info
        setCanRetryFinalizePdf(true); // Allow further retries for the submission
        setPdfFinalizeErrorDetails({ message: errorMessage, details: errorData });
        storeSetImportProgressMessage(`重试提交后台处理请求失败: ${errorMessage} 您可以再次重试提交。`);
        setIsRetryingFinalize(false);
        return;
      }

      const submissionResult = await finalizeSubmitResponse.json();

      if (submissionResult.success && submissionResult.taskId) {
        const msgTaskAccepted = `后端已接受重试的导入任务，ID: ${submissionResult.taskId}。正在等待处理完成...`;
        storeSetImportProgressMessage(msgTaskAccepted);
        // console.log('[PDFImport-Retry] Task accepted by backend after retry, taskId:', submissionResult.taskId, '. Starting to poll status.');

        // Clear submission-specific retry state as submission was successful
        setInternalFinalizeRetryInfo(null);
        setCanRetryFinalizePdf(false);
        setPdfFinalizeErrorDetails(null);
        setIsRetryingFinalize(false);

        // Start polling for task status using the content from retry info
        startPollingTaskStatus(submissionResult.taskId, fileName, importControllerSignal, allEnhancedContentJsonFromRetry, fetchUserArticles);
      } else {
        // Backend did not accept the task or returned unexpected response on retry
        const errorMsg = submissionResult.error || '后端未能成功接受重试的导入任务或返回无效响应。';
        storeSetImportProgressMessage(`重试任务提交失败: ${errorMsg}`);

        setInternalFinalizeRetryInfo(prev => ({ ...prev, message: errorMsg })); // Keep retry info
        setCanRetryFinalizePdf(true); // Allow further retries for the submission
        setPdfFinalizeErrorDetails({ message: errorMsg, details: submissionResult });
        setIsRetryingFinalize(false);
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        // console.log('[PDFImport-Retry] Retry finalize-upload fetch aborted.');
        const msgRetryCancelled = '重试已取消。';
        // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (retryFinalizeUpload, catch abort):', msgRetryCancelled);
        storeSetImportProgressMessage(msgRetryCancelled); // Or rely on global cancel message
        // Global abort handler (e.g., store's cancelPdfImport) should call performFullImportCleanup
        // No need to setCanRetryFinalizePdf(true) here as the whole import is likely being cancelled.
      } else {
        console.error("[PDFImport-Retry] Error during retry finalize-upload:", error); // Keep this error
        const errorMessage = error.message || "重试最终确认时发生未知网络或服务器错误。";
        // console.log('[usePdfImport] Calling setInternalFinalizeRetryInfo with (retryFinalizeUpload, catch error, new message):', errorMessage);
        setInternalFinalizeRetryInfo(prev => ({ ...prev, message: errorMessage }));
        // console.log('[usePdfImport] Calling setCanRetryFinalizePdf(true) from retryFinalizeUpload catch error');
        setCanRetryFinalizePdf(true); // Re-enable retry
        // console.log('[usePdfImport] Calling setPdfFinalizeErrorDetails from retryFinalizeUpload catch error with:', { message: errorMessage, details: error });
        setPdfFinalizeErrorDetails({ message: errorMessage, details: error });

        const msgRetryCatchError = `最终确认重试失败: ${errorMessage} 您可以再次重试。`;
        // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (retryFinalizeUpload, catch error):', msgRetryCatchError);
        storeSetImportProgressMessage(msgRetryCatchError);
      }
      // console.log('[usePdfImport] Calling setIsRetryingFinalize with (retryFinalizeUpload, end of catch / finally):', false);
      setIsRetryingFinalize(false);
    }
  }, [internalFinalizeRetryInfo, isRetryingFinalize, storeSetImportProgressMessage, setCanRetryFinalizePdf, setPdfFinalizeErrorDetails, storeActions.fetchUserArticles, navigate, performFullImportCleanup, storeSetIsImportingPdf]);

  // Register the actual retry function with the store
  // This allows the store action triggerPdfImportRetry to call the logic defined in this hook.
  useEffect(() => {
    useStore.setState({ _actualRetryFn: retryFinalizeUpload });
    return () => {
      useStore.setState({ _actualRetryFn: null }); // Cleanup
    };
  }, [retryFinalizeUpload]); // Add retryFinalizeUpload as a dependency

// Function to poll task status
  const startPollingTaskStatus = (taskId, fileName, signal, allEnhancedContentJsonForStore, fetchUserArticlesFn) => {
console.log('[DEBUG Roo][Polling] startPollingTaskStatus called. TaskId:', taskId, 'FileName:', fileName, 'Signal Aborted:', signal?.aborted);
    // console.log(`[PDFImport-Poll] Starting polling for taskId: ${taskId}`);
    const POLLING_INTERVAL = 3000; // 3 seconds
    let intervalId = null;

    const poll = async () => {
console.log('[DEBUG Roo][Polling] poll function called. TaskId:', taskId, 'Signal Aborted:', signal?.aborted);
      if (signal?.aborted) {
        // console.log(`[PDFImport-Poll] Polling aborted for taskId: ${taskId} (signal check before fetch)`);
        clearInterval(intervalId);
        storeSetImportProgressMessage('导入已取消 (轮询中止)。');
        return;
      }

      try {
        const response = await fetch(`${apiBaseUrl}api/import-pdf/task-status/${taskId}`, {
          method: 'GET',
          credentials: 'include',
          signal: signal,
        });

        if (signal?.aborted) {
          // console.log(`[PDFImport-Poll] Polling aborted for taskId: ${taskId} (signal check after fetch)`);
          clearInterval(intervalId);
          return;
        }

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: '无法获取任务状态，响应无效。' }));
          const errorMsg = errorData.error || `获取任务状态失败: ${response.status}`;
          storeSetImportProgressMessage(`轮询状态错误: ${errorMsg}`);
          if (response.status === 404 || response.status === 403 || response.status === 401) {
              clearInterval(intervalId);
              messageApiApp.error(`无法继续获取导入状态: ${errorMsg} (状态码: ${response.status})`);
              performFullImportCleanup();
          }
          return;
console.log('[DEBUG Roo][Polling] About to call performFullImportCleanup due to polling result/error. Condition: !response.ok and critical status.');
        }

        const taskStatus = await response.json();
console.log('[DEBUG Roo][Polling] taskStatus received:', JSON.stringify(taskStatus));

        if (signal?.aborted) {
            // console.log(`[PDFImport-Poll] Polling aborted for taskId: ${taskId} (signal check after await json)`);
            clearInterval(intervalId);
            return;
        }

        storeSetImportProgressMessage(taskStatus.message || `当前状态: ${taskStatus.status}`);

        if (taskStatus.status === 'completed') {
console.log('[DEBUG Roo][Polling] Task status is completed.');
          clearInterval(intervalId);
          if (taskStatus.success && taskStatus.result && taskStatus.result.newArticleUuid) {
            useStore.setState({
              article: { uuid: taskStatus.result.newArticleUuid, title: taskStatus.result.newArticleTitle || fileName, contentId: taskStatus.result.contentUuid },
              translated: {},
              content: [], // Force ArticleEditPage to refetch content with correct image URLs
              currentTranslationControllers: new Map(),
              isTranslatingAllActive: false,
              isTranslationGloballyCancelled: false,
              indicesToTranslate: [],
              streamingChunkText: {}
            });
            messageApiApp.success({ content: 'PDF 内容已成功导入并处理！正在刷新列表并跳转...', key: 'pdf-process-sidebar', duration: 3 });

            if (fetchUserArticlesFn) {
              await fetchUserArticlesFn();
            }
            performFullImportCleanup();
            navigate(`/article/${taskStatus.result.newArticleUuid}/edit`);
          } else {
            const errorMsg = taskStatus.result?.error || '任务完成但结果无效。';
            console.error(`[PDFImport-Poll] Task ${taskId} completed but with invalid result:`, taskStatus);
            messageApiApp.error(`导入任务完成但结果异常: ${errorMsg}`);
            storeSetImportProgressMessage(`导入失败: ${errorMsg}`);
            performFullImportCleanup();
console.log('[DEBUG Roo][Polling] About to call performFullImportCleanup due to polling result/error. Condition: taskStatus.status === completed but result invalid.');
          }
        } else if (taskStatus.status === 'failed') {
console.log('[DEBUG Roo][Polling] Task status is failed.');
          clearInterval(intervalId);
          const errorMsg = taskStatus.error || '未知错误导致后台导入失败。';
          console.error(`[PDFImport-Poll] Task ${taskId} failed. Error: ${errorMsg}`);
          messageApiApp.error(`PDF 导入失败: ${taskStatus.message || errorMsg}`);
          storeSetImportProgressMessage(`导入失败: ${taskStatus.message || errorMsg}`);
          setPdfFinalizeErrorDetails({ message: (taskStatus.message || errorMsg), details: taskStatus.error });
          setCanRetryFinalizePdf(false);
console.log('[DEBUG Roo][Polling] About to call performFullImportCleanup due to polling result/error. Condition: taskStatus.status === failed.');
          performFullImportCleanup();
        }
      } catch (error) {
        if (signal?.aborted || error.name === 'AbortError') {
          clearInterval(intervalId);
        } else {
          console.error(`[PDFImport-Poll] Error during polling for taskId ${taskId}:`, error);
          storeSetImportProgressMessage(`轮询状态时发生错误: ${error.message}`);
        }
      }
    };

    intervalId = setInterval(poll, POLLING_INTERVAL);
    poll();

    const cleanupPolling = () => {
      clearInterval(intervalId);
    };

    if (signal) {
      signal.addEventListener('abort', cleanupPolling, { once: true });
    }

    return cleanupPolling;
  };
  const handlePdfFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    let referencesTextForUpload = null; // Variable to store extracted references
    let mainContentAfterReferenceExtraction = null; // To store the main content after refs are potentially removed

    // console.log('[PDFImport] PDF file selected:', file.name, 'size:', file.size, 'type:', file.type);

    const {
      setIsImportingPdf,
      setImportProgressMessage: hookSetImportProgressMessage, // Alias to avoid confusion with store's version if used directly
      // setCurrentImportController, // Now using storeSetCurrentImportController
      // clearCurrentImportController, // Now using storeClearCurrentImportController
      fetchUserArticles // For refreshing sidebar
    } = storeActions; // Keep existing ones if they are used locally before store versions

    if (file.type !== 'application/pdf') {
      const msgErrorFile = '请选择一个 PDF 文件。';
      // console.log('[usePdfImport] Calling messageApiApp.error with (handlePdfFileSelect, invalid file type):', msgErrorFile);
      messageApiApp.error(msgErrorFile);
      if (fileInputRef.current) fileInputRef.current.value = "";
      return;
    }

    const importController = new AbortController();
    // console.log('[usePdfImport] Calling storeSetCurrentImportController with new AbortController instance (in handlePdfFileSelect)');
    storeSetCurrentImportController(importController);

    // Reset retry states in the store and locally at the beginning of a new import
    // console.log('[usePdfImport] Calling setCanRetryFinalizePdf with false (in handlePdfFileSelect, reset)');
    setCanRetryFinalizePdf(false);
    // console.log('[usePdfImport] Calling setPdfFinalizeErrorDetails with null (in handlePdfFileSelect, reset)');
    setPdfFinalizeErrorDetails(null);
    // console.log('[usePdfImport] Calling setInternalFinalizeRetryInfo with (in handlePdfFileSelect, reset):', null);
    setInternalFinalizeRetryInfo(null);
    // console.log('[usePdfImport] Calling setIsRetryingFinalize with (in handlePdfFileSelect, reset):', false);
    setIsRetryingFinalize(false);

    // console.log('[usePdfImport] Calling storeSetIsImportingPdf with (in handlePdfFileSelect, start):', true);
    storeSetIsImportingPdf(true);
    const msg1 = '正在准备导入...';
    // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, start):', msg1);
    storeSetImportProgressMessage(msg1);
    // console.log('[PDFImport] Import process started.');

    try {
      const msgParse = '正在解析PDF...';
      // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, parsing pdf):', msgParse);
      storeSetImportProgressMessage(msgParse);
      // console.log('[PDFImport] Reading file into ArrayBuffer...');
      const arrayBuffer = await file.arrayBuffer(); // This is the original ArrayBuffer
      // console.log('[PDFImport] Original ArrayBuffer read, size:', arrayBuffer.byteLength);

      // **立即创建副本供图片提取使用**
      const pdfArrayBufferForImages = arrayBuffer.slice(0);
      // console.log('[PDFImport] Created ArrayBuffer copy for images, size:', pdfArrayBufferForImages.byteLength);

      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');

      // Use the new getPdfText function for text extraction
      // The original arrayBuffer is passed directly
      // console.log('[PDFImport] Extracting text using pdfParser.getPdfText...');
      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');

      let rawTextFromPdf = await getPdfText(arrayBuffer); // Pass arrayBuffer directly
      // The getPdfText function already handles trimming and newline normalization.
      // console.log('[PDFImport] Text extracted from PDF via getPdfText.');

      if (!rawTextFromPdf) {
        const msgErrorEmpty = { content: 'PDF 内容为空或无法解析有效文本。', key: 'pdf-process-sidebar', duration: 3 };
        // console.log('[usePdfImport] Calling messageApiApp.error with (handlePdfFileSelect, empty pdf):', msgErrorEmpty);
        messageApiApp.error(msgErrorEmpty);
        // No need to set value if finally block handles it
        return;
      }
      const msgAiStruct = 'PDF 文本提取完成，准备AI结构化...';
      // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, ai structure):', msgAiStruct);
      storeSetImportProgressMessage(msgAiStruct);

      // 获取系统提示词（这些相对稳定，可以在开始时获取）
      const parseTextPromptFromStore = useStore.getState().parseTextPrompt;
      const storeReferenceParsePrompt = useStore.getState().referenceParsePrompt;
      const systemPrompt = parseTextPromptFromStore;

      // 创建一个函数来动态获取最新的API配置
      const getCurrentApiConfig = () => {
        const currentUser = useStore.getState().user;
        const userApis = useStore.getState().apis;
        const storeDefaultApiModel = useStore.getState().defaultApiModel;

        // 优先使用 store 中的最新配置，如果没有则回退到用户对象中的配置
        let userDefaultApiModel;
        if (storeDefaultApiModel && storeDefaultApiModel[0] && storeDefaultApiModel[1]) {
          userDefaultApiModel = storeDefaultApiModel;
          console.log(`[getCurrentApiConfig] 使用 store 中的最新配置:`, userDefaultApiModel);
        } else {
          userDefaultApiModel = currentUser?.config?.defaultApiModel;
          console.log(`[getCurrentApiConfig] 回退到用户对象中的配置:`, userDefaultApiModel);
        }

        console.log(`[getCurrentApiConfig] 最终使用的API模型配置:`, userDefaultApiModel);
        console.log(`[getCurrentApiConfig] 可用API数量:`, userApis?.length);

        // 使用系统的API配置机制
        let selectedApiConfig = {
          key: 'system-default',
          apiUrl: '/api/v1/chat/completions',
          apiKey: 'INTERNAL_FRONTEND_CALL',
          provider: "系统默认"
        };
        let modelName = 'GLM-4-9B-0414';

        if (userDefaultApiModel && userDefaultApiModel[0] && userDefaultApiModel[0] !== '系统默认') {
          const [providerName, selectedModelName] = userDefaultApiModel;
          console.log(`[getCurrentApiConfig] 尝试使用用户配置: ${providerName} - ${selectedModelName}`);
          const userApi = userApis.find(api => api.provider === providerName);
          if (userApi && userApi.apiUrl && userApi.apiKey && userApi.models && userApi.models.includes(selectedModelName)) {
            selectedApiConfig = {
              key: userApi.uuid || userApi.key,
              apiUrl: userApi.apiUrl,
              apiKey: userApi.apiKey,
              provider: userApi.provider
            };
            modelName = selectedModelName;
            console.log(`[getCurrentApiConfig] 成功使用用户配置: ${selectedApiConfig.provider} - ${modelName}`);
          } else {
            const msgWarnApi = `用户默认API配置 (${providerName}/${selectedModelName}) 无效，将使用系统默认API。`;
            console.log(`[getCurrentApiConfig] 用户配置无效，回退到系统默认: ${msgWarnApi}`);
            // console.log('[usePdfImport] Calling messageApiApp.warn with (handlePdfFileSelect, invalid api config):', msgWarnApi);
            messageApiApp.warn(msgWarnApi, 3);
          }
        } else {
          console.log(`[getCurrentApiConfig] 使用系统默认配置: ${selectedApiConfig.provider} - ${modelName}`);
        }

        return { selectedApiConfig, modelName };
      };

      // ---- Reference Extraction will be moved to AFTER AI structuring ----
      // const systemPromptForReferences = `You are an expert in academic paper analysis. Your task is to identify the "References" or "参考文献" section in the provided text. Return a JSON object with two keys: "references_start_index" and "references_end_index", representing the start and end character indices (0-based) of this section. If no such section is found, return {"references_start_index": -1, "references_end_index": -1}. Do not include any explanatory text outside the JSON object.`;
      
      // const shouldSkipAiCurrent = useStore.getState().shouldSkipAiParsing;

      // if (!shouldSkipAiCurrent) {
      //   storeSetImportProgressMessage('正在提取参考文献...');
      //   const refExtractionResult = await extractReferencesViaAI(
      //     rawTextFromPdf,
      //     selectedApiConfig,
      //     modelName,
      //     systemPromptForReferences,
      //     storeSetImportProgressMessage,
      //     importController.signal
      //   );

      //   if (refExtractionResult.error) {
      //     console.warn('[PDFImport] Failed to extract references:', refExtractionResult.error);
      //     messageApiApp.warn(`参考文献提取失败: ${refExtractionResult.error}. 将继续处理全文。`, 3);
      //     // referencesTextForUpload remains null
      //     // rawTextFromPdf remains the full text
      //   } else {
      //     referencesTextForUpload = refExtractionResult.referencesText;
      //     rawTextFromPdf = refExtractionResult.mainContentText; // Use main content for further processing
      //     console.log('[PDFImport] References extracted, main content updated. Main content length:', rawTextFromPdf.length);
      //     if (referencesTextForUpload) {
      //       console.log('[PDFImport] Extracted References Text Length:', referencesTextForUpload.length);
      //     }
      //   }
      // } else {
      //   console.log('[PDFImport] Skipping reference extraction because AI parsing is skipped.');
      //   // referencesTextForUpload remains null
      //   // rawTextFromPdf remains the full text
      // }
      // ---- END OLD Reference Extraction Location ----

      const textChunks = splitTextIntoChunks(rawTextFromPdf, 3000); // rawTextFromPdf is still the full text here
      const totalChunks = textChunks.length;
      // console.log('[PDFImport] PDF text split into', totalChunks, 'chunks for AI processing.');

      if (totalChunks === 0) {
        const msgErrorChunk = { content: '无法将PDF文本分割成有效的内容块。', key: 'pdf-process-sidebar', duration: 3 };
        // console.log('[usePdfImport] Calling messageApiApp.error with (handlePdfFileSelect, no chunks):', msgErrorChunk);
        messageApiApp.error(msgErrorChunk);
        if (fileInputRef.current) fileInputRef.current.value = "";
        return;
      }

      // console.log(`[PDFImport] Preparing for AI structuring with API: ${selectedApiConfig.provider} - ${modelName}`);

      const callAiForChunk = async (textChunk, chunkIndex, signal, isViaProxy = false) => {
        const MAX_RETRIES = 3;
        const RETRY_DELAY_MS = 1000;
        let attempts = 0;
        // `setImportProgressMessage` is available via closure from `handlePdfFileSelect`

        // 在开始AI处理前检查跳过状态
        const initialSkipCheck = useStore.getState().shouldSkipAiParsing;
        console.log(`[callAiForChunk] 块${chunkIndex + 1}开始前检查跳过状态:`, initialSkipCheck);
        if (initialSkipCheck) {
          console.log(`[callAiForChunk] 块${chunkIndex + 1}跳过AI处理`);
          return { success: false, error: "SKIPPED_BY_USER", originalData: textChunk, index: chunkIndex };
        }
        // `totalChunks` is also available via closure

        if (signal?.aborted) {
          // No throw, return failure object as per new requirement
          return { success: false, error: "OPERATION_CANCELLED", originalData: textChunk, index: chunkIndex, details: "Operation cancelled by user before starting." };
        }

        // 动态获取最新的API配置
        const { selectedApiConfig, modelName } = getCurrentApiConfig();
        console.log(`[callAiForChunk] 块${chunkIndex + 1}使用API配置: ${selectedApiConfig.provider} - ${modelName}`);

        const userInputForAi = `${textChunk}`;
        const messages = [{ role: 'system', content: systemPrompt }, { role: 'user', content: userInputForAi }];

        // 使用高级AI客户端
        let openai;
        let attemptViaProxy;
        let usedBaseURL;

        try {
          const clientInfo = getOpenAIClientForCalling(selectedApiConfig, isViaProxy);
          openai = clientInfo.openai;
          attemptViaProxy = clientInfo.attemptViaProxy;
          usedBaseURL = clientInfo.usedBaseURL;
        } catch (instantiationError) {
          console.error(`[PDFImport] Error instantiating OpenAI client for chunk ${chunkIndex + 1}:`, instantiationError);
          return { success: false, error: "CLIENT_INSTANTIATION_FAILED", originalData: textChunk, index: chunkIndex, details: `AI客户端初始化失败: ${instantiationError.message}` };
        }

        while (attempts < MAX_RETRIES) {
          attempts++;
          if (signal?.aborted) {
            return { success: false, error: "OPERATION_CANCELLED", originalData: textChunk, index: chunkIndex, details: `Operation cancelled by user during attempt ${attempts}.` };
          }

          // 在每次重试前检查跳过状态
          const retrySkipCheck = useStore.getState().shouldSkipAiParsing;
          console.log(`[callAiForChunk] 块${chunkIndex + 1}重试${attempts}前检查跳过状态:`, retrySkipCheck);
          if (retrySkipCheck) {
            console.log(`[callAiForChunk] 块${chunkIndex + 1}在重试${attempts}时跳过AI处理`);
            return { success: false, error: "SKIPPED_BY_USER", originalData: textChunk, index: chunkIndex };
          }

          try {
            // console.log(`[PDFImport] AI processing chunk ${chunkIndex + 1}/${totalChunks}, Attempt ${attempts}`);
            if (attempts > 1) {
                 const msgAiRetry = `AI 处理中：块 ${chunkIndex + 1}/${totalChunks} 正在重试 (${attempts -1}/${MAX_RETRIES-1})`;
                 // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, retry):', msgAiRetry);
                 storeSetImportProgressMessage(msgAiRetry);
            }

            // 使用高级OpenAI客户端进行调用
            let completion;
            try {
              completion = await openai.chat.completions.create({
                model: modelName,
                messages: messages,
                stream: false,
                temperature: 0.1
              }, { signal });
            } catch (error) {
              // 检查是否是CORS错误，如果是且未通过代理，则重试通过代理
              if (
                !attemptViaProxy &&
                selectedApiConfig.key !== 'system-default' &&
                error &&
                (
                  (error instanceof OpenAI.APIConnectionError) ||
                  (error instanceof TypeError && error.message && (error.message.toLowerCase().includes('failed to fetch') || error.message.toLowerCase().includes('networkerror')))
                )
              ) {
                console.warn(`[PDFImport] CORS error detected for API ${selectedApiConfig.provider}, chunk ${chunkIndex + 1}, attempt ${attempts}. Adding to failed list and retrying via proxy. URL: ${usedBaseURL}. Error:`, error.message);
                corsFailedApiUrls.add(selectedApiConfig.apiUrl);
                // 通过代理重试
                return callAiForChunk(textChunk, chunkIndex, signal, true);
              }
              // 对于其他错误，抛出以便外层catch处理
              throw error;
            }

            if (signal?.aborted) { // Check immediately after API call
                return { success: false, error: "OPERATION_CANCELLED", originalData: textChunk, index: chunkIndex, details: `Operation cancelled by user after API call attempt ${attempts}.` };
            }

            let aiGeneratedText = completion.choices?.[0]?.message?.content;

            if (!aiGeneratedText) {
              const errorMessage = `AI未能生成有效内容 (块 ${chunkIndex + 1}, 尝试 ${attempts})`;
              if (attempts >= MAX_RETRIES) {
                const msgAiFail2 = `AI 处理：块 ${chunkIndex + 1} 解析失败。`;
                // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, fail after retries 2):', msgAiFail2);
                storeSetImportProgressMessage(msgAiFail2);
                return { success: false, error: "AI_PROCESSING_FAILED_AFTER_RETRIES", originalData: textChunk, index: chunkIndex, details: errorMessage };
              }
              console.warn(`[PDFImport] ${errorMessage} - Retrying...`); // Keep this warning
              await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
              continue;
            }

            let parsedChunkJson;
            try {
              parsedChunkJson = JSON.parse(aiGeneratedText);
            } catch (e) {
              const match = aiGeneratedText.match(/```json\s*([\s\S]*?)\s*```/);
              if (match && match[1]) {
                try {
                  parsedChunkJson = JSON.parse(match[1]);
                } catch (e2) {
                  const errorMessage = `AI返回的内容无法解析为有效的JSON数组 (块 ${chunkIndex + 1}, 尝试 ${attempts}, 即使在代码块中)`;
                  if (attempts >= MAX_RETRIES) {
                    const msgAiFail3 = `AI 处理：块 ${chunkIndex + 1} 解析失败。`;
                    // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, fail after retries 3):', msgAiFail3);
                    storeSetImportProgressMessage(msgAiFail3);
                    return { success: false, error: "AI_PROCESSING_FAILED_AFTER_RETRIES", originalData: textChunk, index: chunkIndex, details: errorMessage };
                  }
                  console.warn(`[PDFImport] ${errorMessage} - Retrying...`); // Keep this warning
                  await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
                  continue;
                }
              } else {
                const errorMessage = `AI返回的内容无法解析为有效的JSON数组 (块 ${chunkIndex + 1}, 尝试 ${attempts})`;
                if (attempts >= MAX_RETRIES) {
                  const msgAiFail4 = `AI 处理：块 ${chunkIndex + 1} 解析失败。`;
                  // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, fail after retries 4):', msgAiFail4);
                  storeSetImportProgressMessage(msgAiFail4);
                  return { success: false, error: "AI_PROCESSING_FAILED_AFTER_RETRIES", originalData: textChunk, index: chunkIndex, details: errorMessage };
                }
                console.warn(`[PDFImport] ${errorMessage} - Retrying...`); // Keep this warning
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
                continue;
              }
            }

            if (!Array.isArray(parsedChunkJson)) {
              const errorMessage = `AI返回的不是预期的JSON数组格式 (块 ${chunkIndex + 1}, 尝试 ${attempts})`;
              if (attempts >= MAX_RETRIES) {
                const msgAiFail5 = `AI 处理：块 ${chunkIndex + 1} 解析失败。`;
                // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, fail after retries 5):', msgAiFail5);
                storeSetImportProgressMessage(msgAiFail5);
                return { success: false, error: "AI_PROCESSING_FAILED_AFTER_RETRIES", originalData: textChunk, index: chunkIndex, details: errorMessage };
              }
              console.warn(`[PDFImport] ${errorMessage} - Retrying...`); // Keep this warning
              await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
              continue;
            }

            // Validation logic for parsedChunkJson items (simplified - no image processing)
            parsedChunkJson.forEach((item, idx) => {
              let isSuspect = false;
              if (!item || typeof item.tag !== 'string') isSuspect = true;
              else if (item.tag === 'img') {
                // 移除AI返回的图片对象，因为我们现在只处理文字内容
                console.log(`[PDFImport] 移除AI返回的图片对象 (块 ${chunkIndex + 1}, item ${idx}):`, JSON.parse(JSON.stringify(item)));
                isSuspect = true; // 标记为可疑，将在下面被过滤掉
              }
              else { if (typeof item.children === 'undefined') isSuspect = true; }
              if (isSuspect) console.warn(`AI返回的JSON对象结构可能不完全符合预期 (块 ${chunkIndex + 1}, item ${idx}):`, JSON.parse(JSON.stringify(item))); // Keep this warning
            });

            // 过滤掉图片对象，只保留文字内容
            parsedChunkJson = parsedChunkJson.filter((item, idx) => {
              if (item && item.tag === 'img') {
                console.log(`[PDFImport] 过滤掉图片对象 (块 ${chunkIndex + 1}, item ${idx})`);
                return false;
              }
              return item && typeof item.tag === 'string' && typeof item.children !== 'undefined';
            });

            return { success: true, data: parsedChunkJson, index: chunkIndex };

          } catch (error) {
            if (signal?.aborted) { // Catch AbortError from signal or other user-initiated aborts
                 return { success: false, error: "OPERATION_CANCELLED", originalData: textChunk, index: chunkIndex, details: `Operation cancelled by user during attempt ${attempts} (in catch block): ${error.message}` };
            }

            // 其他错误处理
            const errorMessage = `处理块 ${chunkIndex + 1} 尝试 ${attempts} 时发生意外错误: ${error.message}`;
            if (attempts >= MAX_RETRIES) {
              const msgAiFail6 = `AI 处理：块 ${chunkIndex + 1} 解析失败。`;
              // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, fail after retries 6):', msgAiFail6);
              storeSetImportProgressMessage(msgAiFail6);
              return { success: false, error: "AI_PROCESSING_FAILED_AFTER_RETRIES", originalData: textChunk, index: chunkIndex, details: errorMessage };
            }
            console.error(`[PDFImport] 处理块 ${chunkIndex + 1} 尝试 ${attempts} 时发生错误 - 重试中...`, error); // Keep this error
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
          }
        }
        // Should not be reached if logic is correct, but as a fallback:
        const msgAiFail7 = `AI 处理：块 ${chunkIndex + 1} 解析失败。`;
        // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (callAiForChunk, fallback fail):', msgAiFail7);
        storeSetImportProgressMessage(msgAiFail7);
        return { success: false, error: "AI_PROCESSING_FAILED_AFTER_RETRIES", originalData: textChunk, index: chunkIndex, details: "未知错误，所有重试均已用尽。" };
      };

      // console.log('[PDFImport] Starting AI structuring for', totalChunks, 'chunks.');
      const CONCURRENCY_FOR_AI_STRUCTURING = 15;
      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');
      const chunkResultsArray = await processInParallelWithProgress(
        textChunks,
        callAiForChunk,
        CONCURRENCY_FOR_AI_STRUCTURING,
        storeSetImportProgressMessage, // Pass the store's setter
        importController.signal
      );
console.log('[handlePdfFileSelect] processInParallelWithProgress 完成后，检查状态：importController.signal.aborted:', importController.signal.aborted, 'shouldSkipAiParsing:', useStore.getState().shouldSkipAiParsing);
console.log('[handlePdfFileSelect] processInParallelWithProgress 完成后，检查状态：importController.signal.aborted:', importController.signal.aborted, 'shouldSkipAiParsing:', useStore.getState().shouldSkipAiParsing);

      if (importController.signal.aborted && !useStore.getState().shouldSkipAiParsing) {
        console.log('[handlePdfFileSelect] 导入被中止 (非跳过AI解析场景)。');
        throw new Error('Operation cancelled by user.');
      } else if (importController.signal.aborted && useStore.getState().shouldSkipAiParsing) {
        console.log('[handlePdfFileSelect] AI 操作因“跳过解析”被中止，将继续执行跳过AI的后续流程。');
        // 不抛出错误，允许继续到下面的 shouldSkipAiParsing 判断逻辑
      }

      // 检查是否用户在AI处理过程中选择了跳过解析
      const currentShouldSkip = useStore.getState().shouldSkipAiParsing;
      console.log('AI处理完成后检查跳过状态:', currentShouldSkip);
      if (currentShouldSkip) {
        console.log('用户选择跳过AI解析，开始创建简单段落结构');
        const msgSkipAi = '用户选择跳过AI解析，正在创建简单段落结构...';
        storeSetImportProgressMessage(msgSkipAi);

        // 重置跳过解析状态
        useStore.setState({ shouldSkipAiParsing: false });

        // 创建简单的段落结构
console.log('[DEBUG Roo] Before calling createSimpleParagraphStructure. textChunks length:', textChunks?.length);
        let allEnhancedContentJson = createSimpleParagraphStructure(textChunks);

console.log('[DEBUG Roo] After calling createSimpleParagraphStructure. allEnhancedContentJson length:', allEnhancedContentJson?.length);
        if (allEnhancedContentJson?.length > 0) {
          console.log('[DEBUG Roo] Sample of allEnhancedContentJson[0]:', JSON.stringify(allEnhancedContentJson[0]));
        }

        // 基于原PDF位置精确插入图片
        const msgImgProc = '简单结构创建完成，开始基于PDF位置插入图片...';
        storeSetImportProgressMessage(msgImgProc);
        console.log('[PDFImport-SkipAI] 开始基于PDF位置插入图片');
        console.log('[PDFImport-SkipAI] 当前内容项数量:', allEnhancedContentJson?.length);
        console.log('[PDFImport-SkipAI] pdfArrayBufferForImages 可用性:', !!pdfArrayBufferForImages, '大小:', pdfArrayBufferForImages?.byteLength);

        // 使用相同的图片插入逻辑
        allEnhancedContentJson = await insertImagesBasedOnPdfPosition(
          allEnhancedContentJson,
          pdfArrayBufferForImages,
          importController.signal,
          storeSetImportProgressMessage
        );

        const msgSaveBackend = '图片插入完成，正在保存到后端...';
        storeSetImportProgressMessage(msgSaveBackend);

        // 继续后续的保存流程（从上传会话开始）
        const uploadSessionId = `upload-${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 9)}`;
        const CHUNK_SIZE_MB = 1;

        console.log('[DEBUG Roo] Before calling segmentPayload. allEnhancedContentJson length:', allEnhancedContentJson?.length, 'CHUNK_SIZE_MB:', CHUNK_SIZE_MB);
        const msgSegment = `正在将数据分割成约 ${CHUNK_SIZE_MB}MB 的块...`;
        storeSetImportProgressMessage(msgSegment);
        const dataChunks = segmentPayload(allEnhancedContentJson, CHUNK_SIZE_MB);
console.log('[DEBUG Roo] After calling segmentPayload. dataChunks length:', dataChunks?.length);
        if (dataChunks?.length > 0) {
          console.log('[DEBUG Roo] Sample of dataChunks[0] (first 100 chars):', JSON.stringify(dataChunks[0]).substring(0,100));
        }
console.log('[DEBUG Roo] Before calling uploadInChunks. uploadSessionId:', uploadSessionId, 'dataChunks length:', dataChunks?.length, 'fileName:', file?.name);

        // if (importController.signal.aborted) throw new Error('Operation cancelled by user.'); // Commented for Skip AI path

        await uploadInChunks(

          uploadSessionId,
          dataChunks,
          file.name,
          null, // Was importController.signal - for Skip AI path
          storeSetImportProgressMessage
        );
console.log('[DEBUG Roo] After calling uploadInChunks.');

        // if (importController.signal.aborted) throw new Error('Operation cancelled by user.'); // Commented for Skip AI path

        const msgFinalConfirm = '所有数据块提交完成，正在等待后端最终确认...';
        storeSetImportProgressMessage(msgFinalConfirm);

console.log('[DEBUG Roo] Before calling finalize-upload fetch. uploadSessionId:', uploadSessionId, 'fileName:', file?.name, 'totalExpectedChunks:', dataChunks?.length);
        // 调用 finalize-upload API
        const finalizeSubmitResponse = await fetch(`${apiBaseUrl}api/import-pdf/finalize-upload`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            uploadSessionId: uploadSessionId,
            fileName: file.name,
            totalExpectedChunks: dataChunks.length,
            referencesText: referencesTextForUpload // Add extracted references
          }),
          // signal: importController.signal, // Removed for Skip AI path

        });
console.log('[DEBUG Roo] After calling finalize-upload fetch. Response status:', finalizeSubmitResponse?.status);

        // if (importController.signal.aborted) throw new Error('Operation cancelled by user.'); // Commented for Skip AI path

        if (!finalizeSubmitResponse.ok) {
          const errorData = await finalizeSubmitResponse.json().catch(() => ({ error: '提交后台处理请求失败，无法解析错误信息。' }));
          const errorMessage = errorData.error || `提交后台处理请求错误: ${finalizeSubmitResponse.status}`;

          const retryInfoPayload = {
            message: errorMessage,
            uploadSessionId: uploadSessionId,
            fileName: file.name,
            totalExpectedChunks: dataChunks.length,
            allEnhancedContentJson: allEnhancedContentJson
          };
          setInternalFinalizeRetryInfo(retryInfoPayload);
          setCanRetryFinalizePdf(true);
          setPdfFinalizeErrorDetails({ message: errorMessage, details: errorData });
          storeSetImportProgressMessage(`提交后台处理请求失败: ${errorMessage} 您可以重试提交。`);
          return;
        }

        const submissionResult = await finalizeSubmitResponse.json();
console.log('[DEBUG Roo] submissionResult from finalize-upload:', JSON.stringify(submissionResult));
console.log('[DEBUG Roo] Checking submissionResult.success and submissionResult.taskId. Success:', submissionResult?.success, 'TaskId:', submissionResult?.taskId);

        if (submissionResult.success && submissionResult.taskId) {
          const msgTaskAccepted = `后端已接受导入任务，ID: ${submissionResult.taskId}。正在等待处理完成...`;
          storeSetImportProgressMessage(msgTaskAccepted);
          startPollingTaskStatus(submissionResult.taskId, file.name, null, allEnhancedContentJson, fetchUserArticles); // Pass null for signal in skip AI path
        } else {
console.log('[DEBUG Roo] finalize-upload submissionResult was not successful or taskId missing. Error message from submissionResult:', submissionResult?.error);
          const errorMsg = submissionResult.error || '后端未能成功接受导入任务或返回无效响应。';
          storeSetImportProgressMessage(`任务提交失败: ${errorMsg}`);
          setPdfFinalizeErrorDetails({ message: errorMsg, details: submissionResult });
          const retryInfoPayload = {
            message: errorMsg,
            uploadSessionId: uploadSessionId,
            fileName: file.name,
            totalExpectedChunks: dataChunks.length
          };
          setInternalFinalizeRetryInfo(retryInfoPayload);
          setCanRetryFinalizePdf(true);
          setPdfFinalizeErrorDetails({ message: errorMsg, details: submissionResult });
        }
        return; // 跳过AI解析后直接返回
      }

      // chunkResultsArray now contains objects like { success: true/false, data/error, index }
      const successfulChunks = chunkResultsArray.filter(r => r && r.success).map(r => r.data);
      const failedChunksCount = chunkResultsArray.filter(r => r && !r.success).length;

      if (failedChunksCount > 0) {
          console.warn(`[PDFImport] ${failedChunksCount} out of ${totalChunks} chunks failed AI processing after all retries.`); // Keep this warning
          // messageApiApp.warn(`${failedChunksCount} 个文本块AI处理失败，可能影响最终结果。`, 5); // User will see individual messages
      }

      let allEnhancedContentJson = successfulChunks.flat(); // This is the AI-structured full document
      mainContentAfterReferenceExtraction = allEnhancedContentJson; // Initialize with all content

      // console.log('[PDFImport] AI structuring complete. Successfully processed chunks:', successfulChunks.length, 'Total items from successful AI processing:', allEnhancedContentJson.length);

      if (successfulChunks.length === 0 && !currentShouldSkip) { // If AI parsing was not skipped and no chunks succeeded
        throw new Error('所有文本块均未能成功通过AI结构化处理。');
      }
      // If some chunks failed but at least one succeeded (or if AI was skipped), we proceed.
      // The user has already been notified about individual failures if AI was not skipped.

      // ---- START NEW Reference Extraction from Structured Content ----
      if (!currentShouldSkip && allEnhancedContentJson.length > 0) {
        storeSetImportProgressMessage('正在从结构化内容中提取参考文献...');

        // 动态获取最新的API配置用于参考文献提取
        const { selectedApiConfig: refApiConfig, modelName: refModelName } = getCurrentApiConfig();
        console.log(`[PDFImport] 参考文献提取使用API配置: ${refApiConfig.provider} - ${refModelName}`);

        // Placeholder for the new function call
        const structuredRefResult = await extractReferencesFromStructuredContentAI(
          allEnhancedContentJson, // Pass the full structured content
          refApiConfig,
          refModelName, // Or a different model specialized for this?
          storeSetImportProgressMessage,
          importController.signal
        );
        if (structuredRefResult.error) {
          console.warn('[PDFImport] Failed to extract references from structured content:', structuredRefResult.error);
          messageApiApp.warn(`从结构化内容中提取参考文献失败: ${structuredRefResult.error}.`, 3);
          // referencesTextForUpload remains null
          // mainContentAfterReferenceExtraction remains allEnhancedContentJson
        } else {
          referencesTextForUpload = structuredRefResult.extractedReferencesText;
          mainContentAfterReferenceExtraction = structuredRefResult.updatedAllEnhancedContentJson; // This is now the main content
          console.log('[PDFImport] References extracted from structured content. Main content items:', mainContentAfterReferenceExtraction.length);
          if (referencesTextForUpload) {
            console.log('[PDFImport] Extracted References Text (from structured) Length:', referencesTextForUpload.length);

            // AI格式化参考文献
            if (referencesTextForUpload && referencesTextForUpload.trim() !== '') {
              // 再次动态获取最新的API配置用于参考文献格式化
              const { selectedApiConfig: formatApiConfig, modelName: formatModelName } = getCurrentApiConfig();
              console.log(`[PDFImport] 参考文献格式化使用API配置: ${formatApiConfig.provider} - ${formatModelName}`);

              const systemPromptForFormatting = storeReferenceParsePrompt || defaultReferenceParsePromptText; // Use imported constant
              console.log(`[PDFImport] Using prompt for reference formatting: "${systemPromptForFormatting}"`);
              const formattingResult = await formatReferencesViaAI(
                referencesTextForUpload, // Pass the already extracted plain text
                formatApiConfig,
                formatModelName, // Consider if a different model/config is better for formatting
                systemPromptForFormatting,
                importController.signal,
                storeSetImportProgressMessage
              );

              if (formattingResult.error) {
                console.warn('[PDFImport] AI reference formatting failed:', formattingResult.error);
                messageApiApp.warning(`参考文献AI格式化失败: ${formattingResult.error}. 将使用原始提取的文本。`, 3);
                // referencesTextForUpload remains the unformatted text
              } else if (formattingResult.formattedText) {
                console.log('[PDFImport] References successfully formatted by AI. Length:', formattingResult.formattedText.length);
                referencesTextForUpload = formattingResult.formattedText; // Update with formatted text
              } else {
                console.log('[PDFImport] AI reference formatting returned no text, using original.');
                // referencesTextForUpload remains the unformatted text
              }
            }
          }
        }

      } else if (allEnhancedContentJson.length === 0 && !currentShouldSkip) {
        console.log('[PDFImport] No structured content available to extract references from (AI processing might have yielded empty result).');
      } else {
        console.log('[PDFImport] Skipping reference extraction from structured content because AI parsing was skipped or no content.');
      }
      // ---- END NEW Reference Extraction from Structured Content ----


      const msgImgProc = 'AI结构化/参考文献提取完成，开始基于PDF位置插入图片...';
      storeSetImportProgressMessage(msgImgProc);
      console.log('[PDFImport] 开始基于PDF位置的图片插入流程');
      console.log('[PDFImport] 当前内容项数量:', mainContentAfterReferenceExtraction?.length);
      console.log('[PDFImport] pdfArrayBufferForImages 可用性:', !!pdfArrayBufferForImages, '大小:', pdfArrayBufferForImages?.byteLength);

      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');

      // 基于原PDF位置精确插入图片
      mainContentAfterReferenceExtraction = await insertImagesBasedOnPdfPosition(
        mainContentAfterReferenceExtraction,
        pdfArrayBufferForImages,
        importController.signal,
        storeSetImportProgressMessage
      );

      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');
      const msgSaveBackend = '文字内容处理完成，正在保存到后端...';
      storeSetImportProgressMessage(msgSaveBackend);
      console.log('[PDFImport] 文字内容处理完成，准备上传到后端。内容项数量:', mainContentAfterReferenceExtraction.length);

      const uploadSessionId = `upload-${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 9)}`;

      const CHUNK_SIZE_MB = 1;
      const msgSegment = `正在将数据分割成约 ${CHUNK_SIZE_MB}MB 的块...`;
      // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, segmenting):', msgSegment);
      storeSetImportProgressMessage(msgSegment);
      const dataChunks = segmentPayload(mainContentAfterReferenceExtraction, CHUNK_SIZE_MB); // Use mainContentAfterReferenceExtraction

      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');

      if (dataChunks.length === 0 && mainContentAfterReferenceExtraction && mainContentAfterReferenceExtraction.length > 0) {
        console.warn('[PDFImport] segmentPayload returned 0 chunks, but there was data. This might indicate an issue in segmentation logic for small payloads.'); // Keep this warning
      } else if (dataChunks.length === 0 && (!mainContentAfterReferenceExtraction || mainContentAfterReferenceExtraction.length === 0)) {
        // console.log('[PDFImport] No content to upload after AI processing and image handling.');
      }

      // console.log(`[PDFImport] Starting chunked upload for ${dataChunks.length} chunks.`);
      await uploadInChunks(
        uploadSessionId,
        dataChunks,
        file.name,
        importController.signal,
        storeSetImportProgressMessage // Pass the store's setter
      );

      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');

      if ((!mainContentAfterReferenceExtraction || mainContentAfterReferenceExtraction.length === 0) && dataChunks.length === 0) {
        // 如果没有任何内容可以上传，也许可以直接提示用户并结束。
        // 或者，后端 finalize API 应该能够处理这种情况（例如，创建一个空文章或不创建）。
        // 为保持流程一致，我们仍然尝试调用 finalize。
        const msgInfoNoContent = '没有从PDF中提取到有效内容进行保存。将尝试完成导入流程。';
        // console.log('[usePdfImport] Calling messageApiApp.info with (handlePdfFileSelect, no content):', msgInfoNoContent);
        messageApiApp.info(msgInfoNoContent, { key: 'pdf-process-sidebar', duration: 4 });
        // console.log('[PDFImport] No content was available to upload. Proceeding to finalize.');
      } else {
        const msgFinalConfirm = '所有数据块提交完成，正在等待后端最终确认...';
        // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, final confirm):', msgFinalConfirm);
        storeSetImportProgressMessage(msgFinalConfirm);
      }
      // console.log('[PDFImport] All chunks uploaded (or no chunks to upload). Finalizing import with backend.');

      // 调用新的 finalize-upload API
      const finalizeSubmitResponse = await fetch(`${apiBaseUrl}api/import-pdf/finalize-upload`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          uploadSessionId: uploadSessionId,
          fileName: file.name,
          totalExpectedChunks: dataChunks.length,
          referencesText: referencesTextForUpload // Add extracted references
        }),
        signal: importController.signal,
      });

      if (importController.signal.aborted) throw new Error('Operation cancelled by user.');

      if (!finalizeSubmitResponse.ok) {
        const errorData = await finalizeSubmitResponse.json().catch(() => ({ error: '提交后台处理请求失败，无法解析错误信息。' }));
        const errorMessage = errorData.error || `提交后台处理请求错误: ${finalizeSubmitResponse.status}`;

        const retryInfoPayload = { // This retry info is for the finalize-upload *submission* itself
          message: errorMessage,
          uploadSessionId: uploadSessionId,
          fileName: file.name,
          totalExpectedChunks: dataChunks.length,
          allEnhancedContentJson: mainContentAfterReferenceExtraction // Add content for polling success case
        };
        setInternalFinalizeRetryInfo(retryInfoPayload);
        setCanRetryFinalizePdf(true);
        setPdfFinalizeErrorDetails({ message: errorMessage, details: errorData });
        storeSetImportProgressMessage(`提交后台处理请求失败: ${errorMessage} 您可以重试提交。`);
        return; // Allow retry for the submission
      }

      const submissionResult = await finalizeSubmitResponse.json();

      if (submissionResult.success && submissionResult.taskId) {
        const msgTaskAccepted = `后端已接受导入任务，ID: ${submissionResult.taskId}。正在等待处理完成...`;
        storeSetImportProgressMessage(msgTaskAccepted);
        // console.log('[PDFImport] Task accepted by backend, taskId:', submissionResult.taskId, '. Starting to poll status.');
        // Start polling for task status
        // Pass mainContentAfterReferenceExtraction for updating store on final success
        startPollingTaskStatus(submissionResult.taskId, file.name, importController.signal, mainContentAfterReferenceExtraction, fetchUserArticles);
      } else {
        // Backend did not accept the task or returned unexpected response
        const errorMsg = submissionResult.error || '后端未能成功接受导入任务或返回无效响应。';
        storeSetImportProgressMessage(`任务提交失败: ${errorMsg}`);
        // Consider if retry should be enabled here. If backend explicitly failed to create a task, retry might not help.
        // For now, treat as a non-retryable error for the submission part, as the API call itself was "ok".
        // If it's a server-side validation before task creation, it might be retryable if user can fix something.
        // For simplicity, let's make this non-retryable for now, focusing on polling.
        setPdfFinalizeErrorDetails({ message: errorMsg, details: submissionResult });
        // performFullImportCleanup(); // Clean up as the process cannot proceed.
        // No, don't cleanup yet, allow user to see the error. Cleanup will happen if they cancel or start new.
        // Let's allow retry for this specific case as well, as it's a failure in the "finalize-upload" step.
        const retryInfoPayload = {
          message: errorMsg,
          uploadSessionId: uploadSessionId,
          fileName: file.name,
          totalExpectedChunks: dataChunks.length
        };
        setInternalFinalizeRetryInfo(retryInfoPayload);
        setCanRetryFinalizePdf(true);
        setPdfFinalizeErrorDetails({ message: errorMsg, details: submissionResult });
      }

    } catch (error) {
console.error("[DEBUG Roo] handlePdfFileSelect caught an error in main catch block:", error);
        console.error("[DEBUG Roo] Error name:", error?.name);
        console.error("[DEBUG Roo] Error message:", error?.message);
        console.error("[DEBUG Roo] Error stack:", error?.stack);
        // Log additional properties if they exist, e.g., for custom error objects
        if (error && typeof error === 'object') {
          for (const key in error) {
            if (Object.prototype.hasOwnProperty.call(error, key)) {
              console.error(`[DEBUG Roo] Error property ${key}:`, error[key]);
            }
          }
        }
      if (error.name === 'AbortError') {
        const msgWarnCancel = 'PDF 导入已取消。';
        // console.log('[usePdfImport] Calling messageApiApp.warn with (handlePdfFileSelect, catch abort):', msgWarnCancel);
        messageApiApp.warn(msgWarnCancel, { key: 'pdf-process-sidebar', duration: 3 });
        const msgImportCancelled = '导入已取消。';
        // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, catch abort):', msgImportCancelled);
        storeSetImportProgressMessage(msgImportCancelled);
        // console.log('[PDFImport] PDF import process cancelled by user.');
        // abortPdfImportProcess(); // Optionally call the specific abort action from store if different handling is needed
      } else {
        // Any other error not handled by retry mechanism
        console.error("[PDFImport] PDF 导入处理失败 (usePdfImport):", error); // Keep this error
        const msgErrorImportFail = { content: `PDF 导入处理失败: ${error.message}`, key: 'pdf-process-sidebar', duration: 5 };
        // console.log('[usePdfImport] Calling messageApiApp.error with (handlePdfFileSelect, catch general error):', msgErrorImportFail);
        messageApiApp.error(msgErrorImportFail);
        const msgImportFail = `导入失败: ${error.message}`;
        // console.log('[usePdfImport] Calling storeSetImportProgressMessage with (handlePdfFileSelect, catch general error):', msgImportFail);
        storeSetImportProgressMessage(msgImportFail);
      }
      performFullImportCleanup(); // Full cleanup on error or abort
    }
    // No 'finally' block for setIsImportingPdf/clearCurrentImportController, as it's handled conditionally.
  };

  const triggerPdfUpload = () => {
    if (fileInputRef.current) {
      // Before triggering a new upload, ensure any pending retry state is cleared from store and locally.
      const { canRetryFinalizePdf: storeCanRetry } = useStore.getState(); // Check store directly
      if (storeCanRetry || internalFinalizeRetryInfo || isRetryingFinalize) {
          // console.log('[PDFImport] New PDF upload triggered, clearing previous finalize retry state.');
          // console.log('[usePdfImport] Calling setCanRetryFinalizePdf(false) from triggerPdfUpload');
          setCanRetryFinalizePdf(false);
          // console.log('[usePdfImport] Calling setPdfFinalizeErrorDetails(null) from triggerPdfUpload');
          setPdfFinalizeErrorDetails(null);
          // console.log('[usePdfImport] Calling setInternalFinalizeRetryInfo with (in triggerPdfUpload):', null);
          setInternalFinalizeRetryInfo(null);
          // console.log('[usePdfImport] Calling setIsRetryingFinalize with (in triggerPdfUpload):', false);
          setIsRetryingFinalize(false);

          // If an import was "active" in a retry state, ensure it's fully cleaned up.
          // console.log('[usePdfImport] Calling storeSetIsImportingPdf with (in triggerPdfUpload):', false);
          storeSetIsImportingPdf(false);
          // console.log('[usePdfImport] Calling storeClearCurrentImportController (in triggerPdfUpload)');
          storeClearCurrentImportController(); // This also clears retry states in store
      }

      // 确保每次点击都能重新选择文件，即使是同一个文件
      // 通过清空 value 来确保 onChange 事件能够触发
      fileInputRef.current.value = "";
      fileInputRef.current.click();
    }
  };

  // The hook now doesn't need to return canRetryFinalize or finalizeErrorDetails,
  // as App.jsx will get these directly from the store.
  // It still returns retryFinalizeUpload if App.jsx is to call it directly,
  // OR if the store's triggerPdfImportRetry action is set up to call it via the _actualRetryFn ref.
  // Based on the plan, store's triggerPdfImportRetry will call the registered _actualRetryFn.
  return {
    handlePdfFileSelect,
    triggerPdfUpload,
    // retryFinalizeUpload, // Not directly returned if store action handles it.
                           // However, App.jsx might still want it for direct call.
                           // For the store action to work, it must be defined (which it is, and registered).
  };
};

async function replaceImgPlaceholdersInJson(structuredContentJson, pdfDataForImages, signal) { // Added signal parameter
  // console.log('[PDFImport] Entered replaceImgPlaceholdersInJson.');
  // pdfDataForImages is expected to be an ArrayBuffer or Uint8Array, as prepared in handlePdfFileSelect

  try {
    // pdfParser.extractPdfImages will handle ArrayBuffer/Uint8Array conversion internally.
    // We just need to ensure pdfDataForImages is one of them.
    if (!(pdfDataForImages instanceof ArrayBuffer) && !(pdfDataForImages instanceof Uint8Array)) {
      console.error('[PDFImport-replace] Invalid pdfDataForImages type. Expected ArrayBuffer or Uint8Array. Received:', typeof pdfDataForImages); // Keep this error
      return structuredContentJson.filter(item =>
        !(item.tag === 'img' && item['data-pdf-image-placeholder'] === 'true')
      );
    }
    // console.log('[PDFImport-replace] pdfDataForImages is ready for image extraction via pdfParser.extractPdfImages, type:', pdfDataForImages instanceof ArrayBuffer ? 'ArrayBuffer' : 'Uint8Array', 'length:', pdfDataForImages.byteLength);

    if (signal?.aborted) throw new Error('Operation cancelled by user (before image extraction).');

    const placeholderItems = structuredContentJson.filter(item =>
      item.tag === 'img' && item['data-pdf-image-placeholder'] === 'true' // Assumes normalization has occurred
    );
    // console.log(`[PDFImport-replace] Found ${placeholderItems.length} image placeholders in JSON content.`);

    if (placeholderItems.length === 0) {
        // console.log('[PDFImport-replace] No placeholders found, returning original content.');
        return structuredContentJson;
    }

    // Call the new extractPdfImages function from pdfParser.js
    // It expects ArrayBuffer or Uint8Array
    const realImageObjects = await extractPdfImages(pdfDataForImages);
    // console.log('[DEBUG Roo] replaceImgPlaceholdersInJson: Extracted realImageObjects:', JSON.stringify(realImageObjects?.map(img => ({ srcExists: !!img.src, alt: img.alt, srcLength: img.src?.length }))));

    if (signal?.aborted) throw new Error('Operation cancelled by user (after image extraction).');

    if (!realImageObjects || realImageObjects.length === 0) {
      console.warn('[PDFImport-replace] No real images extracted from PDF by pdfParser. All placeholders will be removed.'); // Keep this warning
      return structuredContentJson.filter(item =>
        !(item.tag === 'img' && item['data-pdf-image-placeholder'] === 'true')
      );
    }

    let imageIndex = 0;
    let replacedCount = 0;
    const updatedContentJson = structuredContentJson.map(item => {
      const isStandardPlaceholder = item.tag === 'img' && item['data-pdf-image-placeholder'] === 'true';
      // Check for malformed placeholder: img tag, empty string children, no src, and not already a standard placeholder
      const isMalformedPlaceholder = item.tag === 'img' &&
                                   typeof item.children === 'string' &&
                                   item.children.trim() === '' &&
                                   !item.src &&
                                   !item['data-pdf-image-placeholder'];

      if (isStandardPlaceholder || isMalformedPlaceholder) {
        if (isMalformedPlaceholder) {
          // console.log(`[DEBUG Roo] replaceImgPlaceholdersInJson: Identified malformed placeholder (empty children, no src):`, JSON.stringify(item));
        }
        if (imageIndex < realImageObjects.length) {
          const realImgData = realImageObjects[imageIndex++];
          const newItem = { ...item }; // Start with a copy of the original item
          delete newItem['data-pdf-image-placeholder'];
          delete newItem.children; // Explicitly remove the children property
          newItem.src = realImgData.src;
          newItem.alt = realImgData.alt || item.alt || 'Image from PDF';
          // Explicitly copy width and height if they exist on realImgData, server will clean them later if needed.
          // This ensures if pdfParser provides them, they are at least passed through this stage.
          if (realImgData.width) newItem.width = realImgData.width;
          if (realImgData.height) newItem.height = realImgData.height;
          replacedCount++;
          // Updated log to include the state of 'children' property after deletion
          // console.log('[DEBUG Roo] replaceImgPlaceholdersInJson: Replacing placeholder. NewItem:', JSON.stringify({ srcExists: !!newItem.src, alt: newItem.alt, srcLength: newItem.src?.length, width: newItem.width, height: newItem.height, children: newItem.children }));
          return newItem;
        }
        // console.log(`[DEBUG Roo] replaceImgPlaceholdersInJson: Placeholder for imageIndex ${imageIndex} found, but no more real images. Removing placeholder.`);
        return null;
      }
      return item;
    });

    const finalContent = updatedContentJson.filter(item => item !== null);
    // console.log(`[PDFImport-replace] Image replacement complete. Total placeholders processed: ${placeholderItems.length}, Replaced with real images: ${replacedCount}. Final content length: ${finalContent.length}`);
    return finalContent;

  } catch (error) {
    console.error('[PDFImport-replace] Error in replaceImgPlaceholdersInJson:', error); // Keep this error
    console.warn('[PDFImport-replace] Due to error, attempting to remove all image placeholders and return content.'); // Keep this warning
    // Attempt to filter out any form of placeholder to prevent malformed content
    return structuredContentJson.filter(item => {
        const isStandardPlaceholder = item.tag === 'img' && item['data-pdf-image-placeholder'] === 'true';
        const isMalformedPlaceholderFromLog = item.tag === 'img' &&
                                            typeof item.children === 'string' &&
                                            item.children.trim() === '' &&
                                            !item.src &&
                                            !item['data-pdf-image-placeholder']; // Ensure it's not also a standard one
        return !(isStandardPlaceholder || isMalformedPlaceholderFromLog);
    });
  }
}

// 基于PDF原始位置精确插入图片
async function insertImagesBasedOnPdfPosition(structuredContentJson, pdfDataForImages, signal, setProgressMessage) {
  console.log('[PDFImport-位置插入] 开始基于PDF原始位置插入图片');
  console.log(`[PDFImport-位置插入] 输入内容项数量: ${structuredContentJson ? structuredContentJson.length : 0}`);

  try {
    // 验证输入数据
    if (!(pdfDataForImages instanceof ArrayBuffer) && !(pdfDataForImages instanceof Uint8Array)) {
      console.error('[PDFImport-位置插入] 无效的PDF数据类型:', typeof pdfDataForImages);
      return structuredContentJson;
    }

    console.log(`[PDFImport-位置插入] PDF数据大小: ${pdfDataForImages.byteLength} 字节`);

    if (signal?.aborted) throw new Error('Operation cancelled by user (before image extraction).');

    // 步骤1: 提取PDF中的所有图片（包含页面信息）
    setProgressMessage('正在从PDF中提取图片及位置信息...');
    const realImageObjects = await extractPdfImages(pdfDataForImages);
    console.log(`[PDFImport-位置插入] 从PDF中提取到 ${realImageObjects ? realImageObjects.length : 0} 张图片`);

    // 详细输出图片信息
    if (realImageObjects && realImageObjects.length > 0) {
      realImageObjects.forEach((img, index) => {
        console.log(`[PDFImport-位置插入] 图片 ${index + 1}: 页面=${img.pageNum}, src长度=${img.src?.length}, alt=${img.alt}`);
      });
    }

    if (!realImageObjects || realImageObjects.length === 0) {
      console.log('[PDFImport-位置插入] 未找到图片，返回原始内容');
      return structuredContentJson;
    }

    if (signal?.aborted) throw new Error('Operation cancelled by user (after image extraction).');

    // 步骤2: 提取PDF的页面级文本信息
    setProgressMessage('正在分析PDF页面文本信息...');
    const pageTexts = await getPdfTextByPages(pdfDataForImages);
    console.log(`[PDFImport-位置插入] 获取到 ${pageTexts.length} 页文本信息`);

    if (signal?.aborted) throw new Error('Operation cancelled by user (after text extraction).');

    // 步骤3: 分析内容与页面的对应关系
    setProgressMessage('正在分析内容与页面的对应关系...');
    const contentPageMapping = analyzeContentPageMapping(structuredContentJson, pageTexts);
    console.log(`[PDFImport-位置插入] 完成内容页面映射分析`);

    // 步骤4: 根据页面位置插入图片
    setProgressMessage('正在根据页面位置插入图片...');
    const updatedContent = insertImagesAtPagePositions(
      structuredContentJson,
      realImageObjects,
      contentPageMapping
    );

    console.log(`[PDFImport-位置插入] 图片插入完成，内容项数量: ${updatedContent.length}`);

    // 验证插入结果
    const imageCount = updatedContent.filter(item => item.tag === 'img').length;
    console.log(`[PDFImport-位置插入] 最终内容中包含 ${imageCount} 张图片`);

    return updatedContent;

  } catch (error) {
    console.error('[PDFImport-位置插入] 图片插入过程中发生错误:', error);
    console.warn('[PDFImport-位置插入] 由于错误，返回原始内容');
    return structuredContentJson;
  }
}

// 分析内容与PDF页面的对应关系
function analyzeContentPageMapping(contentJson, pageTexts) {
  const mapping = [];
  const totalPages = pageTexts.length;

  // 为每个内容项分析它最可能属于哪个页面
  contentJson.forEach((item, index) => {
    if (item.tag && item.children && typeof item.children === 'string') {
      const itemText = item.children.trim();

      if (itemText.length > 10) { // 只分析有足够文本的项目
        let bestPageMatch = 1;
        let bestScore = 0;
        let allScores = [];

        // 与每个页面的文本进行相似度比较
        pageTexts.forEach(pageInfo => {
          const score = calculateTextSimilarity(itemText, pageInfo.text);
          allScores.push({ page: pageInfo.pageNum, score });
          if (score > bestScore) {
            bestScore = score;
            bestPageMatch = pageInfo.pageNum;
          }
        });

        // 如果没有找到好的匹配，使用基于位置的估算
        if (bestScore < 0.05) {
          // 根据内容在文档中的位置估算页面
          const estimatedPage = Math.ceil((index / contentJson.length) * totalPages);
          bestPageMatch = Math.max(1, Math.min(estimatedPage, totalPages));
          bestScore = 0.05; // 给一个低但非零的置信度
          console.log(`[PDFImport-位置插入] 内容项 ${index} 使用位置估算，分配到第 ${bestPageMatch} 页`);
        }

        mapping.push({
          contentIndex: index,
          pageNum: bestPageMatch,
          confidence: bestScore,
          text: itemText.substring(0, 100), // 保存前100个字符用于调试
          allScores: allScores.slice(0, 3) // 保存前3个最高分数用于调试
        });
      } else {
        // 对于短文本，使用前一个项目的页面或默认为第1页
        const prevPageNum = mapping.length > 0 ? mapping[mapping.length - 1].pageNum : 1;
        mapping.push({
          contentIndex: index,
          pageNum: prevPageNum,
          confidence: 0.1,
          text: itemText
        });
      }
    }
  });

  // 输出页面分布统计
  const pageDistribution = {};
  mapping.forEach(m => {
    pageDistribution[m.pageNum] = (pageDistribution[m.pageNum] || 0) + 1;
  });

  console.log(`[PDFImport-位置插入] 内容页面映射完成，共分析 ${mapping.length} 个内容项`);
  console.log(`[PDFImport-位置插入] 页面分布:`, pageDistribution);

  return mapping;
}

// 计算文本相似度（改进的关键词匹配）
function calculateTextSimilarity(text1, text2) {
  if (!text1 || !text2) return 0;

  // 将文本转换为小写并分词
  const words1 = text1.toLowerCase().match(/\w+/g) || [];
  const words2 = text2.toLowerCase().match(/\w+/g) || [];

  if (words1.length === 0 || words2.length === 0) return 0;

  // 计算共同词汇的比例（降低词长度要求，提高匹配率）
  const commonWords = words1.filter(word =>
    word.length > 2 && words2.includes(word) // 降低到长度大于2的词
  );

  // 使用更宽松的相似度计算
  const similarity = commonWords.length / Math.min(words1.length, words2.length);

  // 如果有任何匹配，给一个基础分数
  if (commonWords.length > 0) {
    return Math.max(similarity, 0.1); // 至少给0.1的基础分数
  }

  return 0;
}

// 根据页面位置插入图片
function insertImagesAtPagePositions(contentJson, imageObjects, contentPageMapping) {
  const updatedContent = [...contentJson];
  let insertedCount = 0;

  // 按页面分组图片
  const imagesByPage = {};
  imageObjects.forEach(img => {
    if (!imagesByPage[img.pageNum]) {
      imagesByPage[img.pageNum] = [];
    }
    imagesByPage[img.pageNum].push(img);
  });

  // 按页面分组内容映射
  const contentByPage = {};
  contentPageMapping.forEach(mapping => {
    if (!contentByPage[mapping.pageNum]) {
      contentByPage[mapping.pageNum] = [];
    }
    contentByPage[mapping.pageNum].push(mapping);
  });

  // 按页面顺序插入图片（确保位置计算正确）
  const sortedPageNums = Object.keys(imagesByPage).sort((a, b) => parseInt(a) - parseInt(b));

  sortedPageNums.forEach(pageNum => {
    const pageImages = imagesByPage[pageNum];
    const pageContent = contentByPage[pageNum] || [];

    console.log(`[PDFImport-位置插入] 处理第${pageNum}页，有 ${pageImages.length} 张图片，${pageContent.length} 个内容项`);

    if (pageContent.length > 0) {
      // 在该页面内容的中间位置插入图片
      const baseInsertPosition = pageContent[Math.floor(pageContent.length / 2)].contentIndex;

      pageImages.forEach((imageObj, imgIndex) => {
        const imageItem = {
          tag: 'img',
          src: imageObj.src,
          alt: imageObj.alt || `第${pageNum}页图片 ${imgIndex + 1}`,
          width: imageObj.width,
          height: imageObj.height
        };

        // 计算实际插入位置，考虑之前插入的图片
        const actualInsertPos = Math.min(baseInsertPosition + insertedCount + imgIndex, updatedContent.length);
        updatedContent.splice(actualInsertPos, 0, imageItem);

        console.log(`[PDFImport-位置插入] 在位置 ${actualInsertPos} 插入第${pageNum}页的图片 ${imgIndex + 1}`);
        console.log(`[PDFImport-位置插入] 图片信息: src长度=${imageObj.src?.length}, alt=${imageItem.alt}`);
      });

      insertedCount += pageImages.length;
    } else {
      // 如果没有找到对应页面的内容，在文档末尾插入
      pageImages.forEach((imageObj, imgIndex) => {
        const imageItem = {
          tag: 'img',
          src: imageObj.src,
          alt: imageObj.alt || `第${pageNum}页图片 ${imgIndex + 1}`,
          width: imageObj.width,
          height: imageObj.height
        };

        updatedContent.push(imageItem);
        console.log(`[PDFImport-位置插入] 在文档末尾插入第${pageNum}页的图片 ${imgIndex + 1}`);
        console.log(`[PDFImport-位置插入] 图片信息: src长度=${imageObj.src?.length}, alt=${imageItem.alt}`);
      });
    }
  });

  console.log(`[PDFImport-位置插入] 总共插入了 ${insertedCount} 张图片`);
  return updatedContent;
}

// The function `extractPdfImagesAsDataURIs` (previously lines 575-875) is now removed.
// Its functionality is replaced by `extractPdfImages` imported from `../utils/pdfParser.js`.