import { useState, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';
import { App } from 'antd';
import { useStore } from '../stores/index.js';
import { content2Html, html2content } from '../utils/index.js';

export const useItemEditControls = () => {
  const { message: messageApi } = App.useApp();
  
  // Store functions
  const updateItemAt = useStore(state => state.updateItemAt);
  const clearItemAt = useStore(state => state.clearItemAt);
  const insertItemAt = useStore(state => state.insertItemAt);
  const deleteItemAt = useStore(state => state.deleteItemAt);
  const updateTranslatedItemAt = useStore(state => state.updateTranslatedItemAt);
  const clearTranslatedItemAt = useStore(state => state.clearTranslatedItemAt);
  const insertTranslatedItemAt = useStore(state => state.insertTranslatedItemAt);

  // 编辑状态：{ type: 'original' | 'translated', chunkIndex, itemIndex }
  const [editingState, setEditingState] = useState({ type: null, chunkIndex: null, itemIndex: null });
  const [editText, setEditText] = useState('');

  // 直接设置编辑文本，不使用防抖，以提供更好的输入体验
  const handleEditTextChange = useCallback((value) => {
    setEditText(value);
  }, []);

  // 开始编辑单个item
  const handleItemEditStart = useCallback((type, chunkIndex, itemIndex, currentItem) => {
    setEditingState({ type, chunkIndex, itemIndex });

    let initialHtml = '';
    if (currentItem === null) {
      initialHtml = '{null}';
    } else if (currentItem) {
      initialHtml = content2Html([currentItem]);
    } else {
      initialHtml = '<p></p>';
    }

    setEditText(initialHtml);
  }, []);

  // 取消编辑
  const handleItemEditCancel = useCallback(() => {
    setEditingState({ type: null, chunkIndex: null, itemIndex: null });
    setEditText('');
  }, []);

  // 保存单个item的编辑
  const handleItemEditSave = useCallback(async () => {
    const { type, chunkIndex, itemIndex } = editingState;
    if (type === null || chunkIndex === null || itemIndex === null) return;

    const saveMessageKey = `saving-item-${type}-${chunkIndex}-${itemIndex}`;
    messageApi.loading({ content: '保存中...', key: saveMessageKey, duration: 0 });

    try {
      let newItemData = null;
      
      // 处理特殊标记
      if (editText.trim() === '{null}') {
        newItemData = null;
      } else if (editText.trim() === '{空行}' || editText.trim() === '') {
        newItemData = { tag: 'p', children: '' };
      } else {
        // 解析HTML内容
        const parsedContent = html2content(editText);
        if (parsedContent && parsedContent.length > 0) {
          newItemData = parsedContent[0]; // 只取第一个item
        } else {
          throw new Error('无法解析内容');
        }
      }

      // 根据类型调用相应的更新函数
      if (type === 'original') {
        await updateItemAt(itemIndex, newItemData);
      } else if (type === 'translated') {
        await updateTranslatedItemAt(itemIndex, newItemData);
      }

      messageApi.success({ 
        content: `${type === 'original' ? '原文' : '译文'}项目已保存`, 
        key: saveMessageKey, 
        duration: 2 
      });
      handleItemEditCancel();
    } catch (error) {
      messageApi.error({ 
        content: `保存失败: ${error.message}`, 
        key: saveMessageKey, 
        duration: 5 
      });
    }
  }, [editingState, editText, updateItemAt, updateTranslatedItemAt, messageApi, handleItemEditCancel]);

  // 插入新item
  const handleItemInsert = useCallback(async (type, chunkIndex, insertIndex) => {
    const messageKey = `inserting-item-${type}-${chunkIndex}-${insertIndex}`;
    messageApi.loading({ content: '插入中...', key: messageKey, duration: 0 });

    try {
      const newItem = { tag: 'p', children: '' }; // 默认插入空段落
      
      if (type === 'original') {
        await insertItemAt(insertIndex, newItem);
      } else if (type === 'translated') {
        await insertTranslatedItemAt(insertIndex, newItem);
      }

      messageApi.success({ 
        content: `已插入新的${type === 'original' ? '原文' : '译文'}项目`, 
        key: messageKey, 
        duration: 2 
      });
    } catch (error) {
      messageApi.error({ 
        content: `插入失败: ${error.message}`, 
        key: messageKey, 
        duration: 5 
      });
    }
  }, [insertItemAt, insertTranslatedItemAt, messageApi]);

  // 删除item（只对原文有效）
  const handleItemDelete = useCallback(async (chunkIndex, itemIndex) => {
    const messageKey = `deleting-item-${chunkIndex}-${itemIndex}`;
    messageApi.loading({ content: '删除中...', key: messageKey, duration: 0 });

    try {
      await deleteItemAt(itemIndex);
      messageApi.success({ 
        content: '项目已删除', 
        key: messageKey, 
        duration: 2 
      });
    } catch (error) {
      messageApi.error({ 
        content: `删除失败: ${error.message}`, 
        key: messageKey, 
        duration: 5 
      });
    }
  }, [deleteItemAt, messageApi]);

  // 清空item内容
  const handleItemClear = useCallback(async (type, chunkIndex, itemIndex) => {
    const messageKey = `clearing-item-${type}-${chunkIndex}-${itemIndex}`;
    messageApi.loading({ content: '清空中...', key: messageKey, duration: 0 });

    try {
      if (type === 'original') {
        await clearItemAt(itemIndex);
      } else if (type === 'translated') {
        await clearTranslatedItemAt(itemIndex);
      }

      messageApi.success({ 
        content: `${type === 'original' ? '原文' : '译文'}项目已清空`, 
        key: messageKey, 
        duration: 2 
      });
    } catch (error) {
      messageApi.error({ 
        content: `清空失败: ${error.message}`, 
        key: messageKey, 
        duration: 5 
      });
    }
  }, [clearItemAt, clearTranslatedItemAt, messageApi]);

  return {
    editingState,
    editText,
    handleEditTextChange,
    handleItemEditStart,
    handleItemEditCancel,
    handleItemEditSave,
    handleItemInsert,
    handleItemDelete,
    handleItemClear,
  };
};
