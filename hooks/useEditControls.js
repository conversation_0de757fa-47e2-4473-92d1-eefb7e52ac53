import { useState, useCallback, useMemo } from 'react';
import { App } from 'antd';
// OpenAI import might be redundant if getOpenAIClientForCalling handles it fully
// import OpenAI from 'openai';
import { useStore } from '../stores/index.js';
import { html2content, content2Html, corsFailedApiUrls, getOpenAIClientForCalling } from '../utils/index.js'; // Import new utils
import { formatTextWithAI } from '../utils/ai.js'; // Added for AI refactoring
import { apiBaseUrl } from '../utils/constant.js';
import { defaultParseTextPromptText } from '../stores/prompt';
import debounce from 'lodash.debounce';

export const useEditControls = (
  chunked, // 当前文章的分块内容
  allChunkTranslatedItems, // 所有分块的翻译内容
  updateOriginalChunk, // 更新原始分块的函数 (来自 Zustand store)
  updateTranslatedChunk, // 更新翻译分块的函数 (来自 Zustand store)
  // messageApi, // antd App.useApp() -> message (直接从 ArticleEditPage 传递)
  // modal, // antd App.useApp() -> modal (直接从 ArticleEditPage 传递)
  // storeParseTextPrompt, // Zustand store -> parseTextPrompt (通过 useStore.getState() 获取)
  // storeDefaultApiModel, // Zustand store -> defaultApiModel (通过 useStore.getState() 获取)
  // storeApis // Zustand store -> apis (通过 useStore.getState() 获取)
) => {
  const { message: messageApi, modal } = App.useApp();
  const storeUpdateOriginalChunk = useStore(state => state.updateOriginalChunk);
  const storeUpdateTranslatedChunk = useStore(state => state.updateTranslatedChunk);

  const [editingState, setEditingState] = useState({ type: null, index: null });
  const [editText, setEditText] = useState('');
  const [isLoading, setIsLoading] = useState(false); // 用于AI转换时的加载状态

  const debouncedSetEditText = useMemo(
    () => debounce((value) => {
      setEditText(value);
    }, 300),
    []
  );

  const handleEditStart = useCallback((type, index) => {
    setEditingState({ type, index });
    debouncedSetEditText.cancel();
    let initialHtml = '';
    if (type === 'original') {
      initialHtml = content2Html(chunked[index]);
    } else if (type === 'translated') {
      try {
        const translatedItemsForChunk = allChunkTranslatedItems[index] || [];
        const validTranslatedItems = translatedItemsForChunk.filter(item => item !== undefined && item !== null);
        const structuredContent = validTranslatedItems.map(item => {
          if (typeof item === 'string') {
            const parsed = html2content(item);
            return parsed.length > 0 ? parsed[0] : { tag: 'p', children: item };
          } else if (item && item.tag) {
            if (item.tag === 'img' && item.src) {
              return { tag: 'img', src: item.src, alt: item.alt || '' };
            }
            if (item.tag === 'table') {
              return { tag: 'table', children: item.children || '' };
            }
            return { ...item };
          }
          return null;
        }).filter(Boolean);
        initialHtml = structuredContent.length > 0 ? content2Html(structuredContent) : '<p></p>';
      } catch (e) {
        console.error("[useEditControls handleEditStart] Error preparing translated content for editing:", e);
        initialHtml = "<!-- Error converting content -->";
      }
    }
    setEditText(initialHtml);
  }, [chunked, allChunkTranslatedItems, debouncedSetEditText]);

  const handleEditCancel = useCallback(() => {
    setEditingState({ type: null, index: null });
    setEditText('');
    setIsLoading(false); // 取消编辑时也重置加载状态
  }, []);

  const proceedWithSave = useCallback(async (type, index, contentToSave, originalItems) => {
    const saveMessageKey = `saving-${type}-${index}`;
    messageApi.loading({ content: '保存中...', key: saveMessageKey, duration: 0 });
    try {
      let showMismatchWarning = false;
      if (type === 'original') {
        await storeUpdateOriginalChunk(originalItems, contentToSave);
      } else if (type === 'translated') {
        if (contentToSave.length !== originalItems.length) showMismatchWarning = true;
        await storeUpdateTranslatedChunk(originalItems, contentToSave);
      }
      const successMessage = `${type === 'original' ? '原文' : '译文'}区块 ${index} 已保存`;
      const warningText = showMismatchWarning ? ' (提示: 原文与译文段数不一致)' : '';
      messageApi.success({ content: successMessage + warningText, key: saveMessageKey, duration: 3 });
      handleEditCancel();
    } catch (error) {
      messageApi.error({ content: `保存失败: ${error.message}`, key: saveMessageKey, duration: 5 });
    }
  }, [storeUpdateOriginalChunk, storeUpdateTranslatedChunk, handleEditCancel, messageApi]);

  const handleAttemptAIFormatConversion = useCallback(async (textToConvert, targetType, targetIndex, originalChunkItems) => {
    const { parseTextPrompt, defaultApiModel, apis } = useStore.getState();
    const aiMessageKey = `ai-converting-${targetType}-${targetIndex}`; // Removed isViaProxy from key
    messageApi.loading({ content: 'AI 正在转换格式...', key: aiMessageKey, duration: 0 });
    setIsLoading(true);

    try {
      const systemPromptForAI = parseTextPrompt || defaultParseTextPromptText;
      const userPromptTemplateForJson = `请将以下HTML文本转换为JSON对象数组，每个对象代表一个内容块，包含'tag'和'children'等属性，确保输出是纯JSON格式，不包含任何额外的解释或Markdown标记。HTML文本如下：\n\n\${text}`;

      let providerName, modelName, selectedApiConfig;

      if (defaultApiModel && defaultApiModel[0] && defaultApiModel[1]) {
        [providerName, modelName] = defaultApiModel;
        if (providerName === '系统默认') {
          selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
          if (!selectedApiConfig.models.includes(modelName)) modelName = selectedApiConfig.models[0];
        } else {
          selectedApiConfig = apis.find(api => api.provider === providerName);
        }
      } else {
        providerName = '系统默认'; modelName = 'GLM-4-9B-0414';
        selectedApiConfig = { key: 'system-default', provider: '系统默认', apiUrl: '/api/v1/chat/completions', apiKey: '', models: ['GLM-4-9B-0414'] };
      }

      if (!selectedApiConfig || (selectedApiConfig.key !== 'system-default' && (!selectedApiConfig.apiUrl || !selectedApiConfig.apiKey)) || (selectedApiConfig.key === 'system-default' && !selectedApiConfig.apiUrl) || !selectedApiConfig.models || !selectedApiConfig.models.includes(modelName)) {
        throw new Error(`AI模型配置不完整或无效: ${providerName} - ${modelName}`);
      }

      // Call formatTextWithAI which handles client instantiation and CORS retries
      const aiResult = await formatTextWithAI(
        textToConvert,
        systemPromptForAI,
        selectedApiConfig,
        modelName,
        null // signal - AbortSignal can be added if cancellation is needed
        // userPromptTemplateForJson is not directly passed; textToConvert is the user content.
        // isViaProxyInternal and temperature options are not params of the current formatTextWithAI signature.
      );

      if (!aiResult.success) {
        throw new Error(aiResult.error || 'AI 格式转换失败 (via formatTextWithAI)');
      }

      const aiResponse = aiResult.data?.trim();

      if (!aiResponse) throw new Error('AI 未返回有效内容 (via formatTextWithAI)。');

      let convertedContentArray;
      try {
        const cleanedJsonResponse = aiResponse.replace(/^```json\s*|```\s*$/g, '');
        convertedContentArray = JSON.parse(cleanedJsonResponse);
        if (!Array.isArray(convertedContentArray) || !convertedContentArray.every(item => typeof item === 'object' && item.tag)) {
          throw new Error('AI 返回的 JSON 结构不符合预期的数组对象格式。');
        }
      } catch (parseError) {
        console.error("AI response parsing error:", parseError, "Raw AI response:", aiResponse);
        throw new Error(`AI 返回内容解析失败: ${parseError.message}。请检查AI输出或尝试手动修正。`);
      }

      messageApi.success({ content: 'AI 格式转换成功!', key: aiMessageKey, duration: 2 });
      await proceedWithSave(targetType, targetIndex, convertedContentArray, originalChunkItems);

    } catch (error) {
      console.error("AI format conversion error:", error);
      messageApi.error({ content: `AI 转换失败: ${error.message}. 请尝试手动修正或再次尝试。`, key: aiMessageKey, duration: 5 });
    } finally {
      setIsLoading(false);
    }
  }, [proceedWithSave, messageApi, modal]); // Removed store dependencies, will use getState

  // Helper function to check if text contains HTML tags
  const containsHtmlTags = (text) => {
    return /<[^>]+>/g.test(text);
  };

  // Helper function to convert plain text to HTML with <p> tags
  const convertPlainTextToHtml = (text) => {
    if (!text || text.trim() === '') return '';

    // Split by line breaks and filter out empty lines
    const lines = text.split(/\r?\n/).filter(line => line.trim() !== '');

    // Wrap each line in <p> tags
    return lines.map(line => `<p>${line.trim()}</p>`).join('\n');
  };

  const handleEditSave = useCallback(async () => {
    const { type, index } = editingState;
    if (type === null || index === null) return;

    const originalItemsInChunk = chunked[index];
    let processedText = editText;
    let newContentArray;
    let conversionOk = true;

    // Pre-process: If input is plain text (no HTML tags), convert to HTML with <p> tags
    if (editText.trim() !== '' && !containsHtmlTags(editText.trim())) {
      processedText = convertPlainTextToHtml(editText);
      console.log('[useEditControls] 检测到纯文本输入，已自动添加<p>标签:', processedText);
    }

    try {
      newContentArray = html2content(processedText);
      if (processedText.trim() !== '' && (!newContentArray || newContentArray.length === 0)) {
        conversionOk = false;
      } else if (Array.isArray(newContentArray) && newContentArray.length > 0 && !newContentArray.every(item => item && typeof item.tag === 'string')) {
        conversionOk = false;
      }
      if (newContentArray === null || typeof newContentArray === 'undefined') {
         conversionOk = false;
      }
    } catch (e) {
      console.error("Error during html2content:", e);
      conversionOk = false;
    }

    if (conversionOk) {
      await proceedWithSave(type, index, newContentArray, originalItemsInChunk);
    } else {
      modal.confirm({
        title: '格式不符合规范',
        content: '您输入的内容格式似乎不正确，无法直接保存。是否尝试使用 AI 转换格式并保存？',
        okText: 'AI转换并保存',
        cancelText: '取消',
        onOk: async () => {
          // Use the original editText for AI conversion, not the processed text
          await handleAttemptAIFormatConversion(editText, type, index, originalItemsInChunk);
        },
        onCancel: () => {
          messageApi.info('操作已取消，请修正内容格式后重试保存。');
        },
      });
    }
  }, [editingState, editText, chunked, proceedWithSave, messageApi, handleAttemptAIFormatConversion, modal]);

  return {
    editingState,
    setEditingState, // Expose if needed externally, though typically managed by handleEditStart/Cancel
    editText,
    setEditText, // Expose if needed, though debounced version is primary
    isLoading, // Expose AI conversion loading state
    debouncedSetEditText,
    handleEditStart,
    handleEditCancel,
    handleEditSave,
    // handleAttemptAIFormatConversion is internal to handleEditSave, no need to export usually
  };
};