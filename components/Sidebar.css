/* Sidebar article delete button hover effect */
.article-menu-item .article-delete-button {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.article-menu-item:hover .article-delete-button {
  visibility: visible;
  opacity: 1;
}
/* 日期分组样式 */
.date-group-header {
  padding: 4px 12px;
  fontSize: 11px;
  fontWeight: 400;
  color: #999;
  marginBottom: 4px;
}

/* 文章列表项样式 */
.ant-list-item {
  transition: background-color 0.2s ease !important;
}

.ant-list-item:hover {
  background-color: #f0f0f0 !important;
}

/* 保留原有的菜单项样式作为备用 */
.article-menu-item.ant-menu-item {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
  padding-left: 16px !important; /* Standard padding for items, adjust if icons look off */
  padding-right: 8px !important; /* Reduced to give more space to text before delete icon */
  line-height: 1.3 !important; /* Tighter line height */
  height: auto !important; /* Ensure height is not fixed and wraps content */
}

/* Specifically target the text element for line-height to ensure it applies */
.article-menu-item .ant-menu-title-content .ant-typography {
  line-height: 1.3 !important; /* Match the item's line height */
  /* This ensures the text itself adheres to the new compact line height */
}

/* Optional: Adjust hover/selected states if they add extra visual spacing due to background or borders */
/*
.article-menu-item.ant-menu-item:hover,
.article-menu-item.ant-menu-item-selected {
  background-color: #f0f0f0 !important; // Example: use a flatter background
}
*/

/* Sidebar action buttons (URL, File, New Blank) */
.sidebar-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
  padding: 4px 6px;
}

.sidebar-action-button .anticon { /* Target Ant Design icons within the button */
  font-size: 20px;
}

.sidebar-action-button span { /* Target the text span within the button */
  font-size: 11px;
}