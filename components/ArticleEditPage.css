/* Consolidated styles for ArticleEditPage (including former ArticleTopBar, ArticleControls, and ArticleChunk) */

/* General styles */
.article-edit-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Controls styles */
.article-controls {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  min-height: 60px; /* 确保最小高度 */
  gap: 8px; /* 添加间距 */
  align-items: flex-start; /* 顶部对齐 */
}

.controls-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* Content styles */
.t-content {
  width: 100%;
}

.t-content img {
  width: 90%;
}

.t-content table,
.t-content td,
.t-content table th {
  border: 1px solid #000;
  border-collapse: collapse;
}

.t-content div:has(> table) {
  zoom: 0.6;
  width: 100%;
  max-height: 400px;
  overflow: auto;
}

.t-content p{
  font-size: 13px;
}

/* AI总结区域字体大小与原文、译文保持一致 */
.summary-content-container p,
.summary-content-container div,
.summary-content-container span {
  font-size: 13px !important;
}

.t-content figcaption{
  font-size:12px;
color:gray;
display:block;
}

.t-content h1{
  font-size:17px;
}

.t-content h2{
  font-size:15px;
}
/* Chunk styles */
.chunk-row {
  width: 100%;
  align-items: stretch;
  margin-bottom: 10px;
}

/* Content container styles */
.original-content-container,
.translated-content-container,
.summary-content-container {
  display: flex;
  flex-direction: column;
  min-height: 200px;
  flex-grow: 1;
}

/* Summary content container specific styles */
.summary-content-container {
  min-height: 60px;
}

/* Editing mode styles */
.original-content-container.editing-mode,
.translated-content-container.editing-mode,
.summary-content-container.editing-mode {
  height: 100%;
  min-height: 300px;
}

.summary-content-container.editing-mode {
  min-height: 120px;
}

.original-content-container.editing-mode .ant-input-textarea,
.translated-content-container.editing-mode .ant-input-textarea,
.summary-content-container.editing-mode .ant-input-textarea {
  height: 100%;
}

.original-content-container.editing-mode .ant-input,
.translated-content-container.editing-mode .ant-input {
  height: 100% !important;
  min-height: 300px !important;
  resize: vertical;
}

/* Reduce vertical spacing within translated table cells */
.translated-content-container table p {
  margin-block-start: 0.2em;
  margin-block-end: 0.2em;
  padding: 0;
  line-height: 1.3;
}

/* Ensure table cells don't add excessive padding */
.translated-content-container table td,
.translated-content-container table th {
  padding: 2px 4px;
}

/* Style for the streaming content */
.streaming-content-pre {
  overflow-y: auto;
  display: block;
  box-sizing: border-box;
}

/* Reduce margin around table images */
.table-image-container {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* Tag count mismatch warning */
.tag-count-mismatch-warning {
  background-color: #FFFBE6;
  padding: 12px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #000000;
}

.tag-count-mismatch-warning .anticon {
  margin-right: 4px;
}
/* Style for icon buttons in the title section to have no hover/focus background */
.icon-title-button:hover,
.icon-title-button:focus {
  background-color: transparent !important;
}

.custom-small-font *{
  font-size: 13px !important; /* Use !important to ensure override */
}

/* Additional styles moved from index.css */

/* Target the content wrappers inside each chunk row */
.original-chunk-content,
.translated-chunk-content {
  width: 100%; /* Ensure they take full width of their container */
  min-height: 2.5em; /* Set a minimum height (adjust as needed, approx 2 lines) */
  padding: 5px; /* Add some internal padding */
  vertical-align: top; /* Align content to the top */
  /* border: 1px dashed #eee; */ /* Optional: Add border for visualization during development */
  margin-bottom: 10px; /* Add space below each chunk content */
}

/* Ensure elements within the wrappers start from the top */
.original-chunk-content > *,
.translated-chunk-content > * {
  margin-top: 0; /* Remove default top margin of first element */
}
