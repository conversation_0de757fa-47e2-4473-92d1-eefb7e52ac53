import { useState, useEffect } from 'react';
import { Modal, Input, Button, Typography, Tabs, Space, Divider } from 'antd';
import { EditOutlined, SaveOutlined, UndoOutlined, FileTextOutlined } from '@ant-design/icons';
import EnhancedTextArea from './EnhancedTextArea';

const { Text, Title } = Typography;

const FullTextEditModal = ({ 
  open, 
  onCancel, 
  onSave,
  originalContent = '',
  translatedContent = '',
  title = '全文编辑'
}) => {
  const [originalText, setOriginalText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [activeTab, setActiveTab] = useState('original');
  const [hasChanges, setHasChanges] = useState(false);

  // 初始化文本内容
  useEffect(() => {
    if (open) {
      setOriginalText(originalContent);
      setTranslatedText(translatedContent);
      setHasChanges(false);
    }
  }, [open, originalContent, translatedContent]);

  // 检测是否有变更
  useEffect(() => {
    const hasOriginalChanges = originalText !== originalContent;
    const hasTranslatedChanges = translatedText !== translatedContent;
    setHasChanges(hasOriginalChanges || hasTranslatedChanges);
  }, [originalText, translatedText, originalContent, translatedContent]);

  const handleSave = () => {
    onSave({
      originalText,
      translatedText,
      hasOriginalChanges: originalText !== originalContent,
      hasTranslatedChanges: translatedText !== translatedContent
    });
  };

  const handleReset = () => {
    setOriginalText(originalContent);
    setTranslatedText(translatedContent);
    setHasChanges(false);
  };

  const handleCancel = () => {
    if (hasChanges) {
      Modal.confirm({
        title: '确认关闭',
        content: '您有未保存的更改，确定要关闭吗？',
        okText: '确认关闭',
        cancelText: '继续编辑',
        onOk: () => {
          handleReset();
          onCancel();
        }
      });
    } else {
      onCancel();
    }
  };

  const getWordCount = (text) => {
    if (!text) return 0;
    // 简单的字符计数，包括中英文
    return text.length;
  };

  const tabItems = [
    {
      key: 'original',
      label: (
        <Space>
          <FileTextOutlined />
          <span>原文编辑</span>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ({getWordCount(originalText)} 字符)
          </Text>
        </Space>
      ),
      children: (
        <div style={{ height: '60vh', display: 'flex', flexDirection: 'column' }}>
          <div style={{ marginBottom: '12px' }}>
            <Text type="secondary" style={{ fontSize: '13px' }}>
              编辑原文内容，支持 HTML 标签和格式
            </Text>
          </div>
          <EnhancedTextArea
            value={originalText}
            onChange={(e) => setOriginalText(e.target.value)}
            placeholder="请输入原文内容..."
            style={{
              flex: 1,
              fontSize: '14px',
              lineHeight: '1.6',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              marginRight: '8px' // 给滚动条留出空间
            }}
            showCount={{
              formatter: ({ count }) => `${count} 字符`
            }}
          />
        </div>
      )
    },
    {
      key: 'translated',
      label: (
        <Space>
          <EditOutlined />
          <span>译文编辑</span>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ({getWordCount(translatedText)} 字符)
          </Text>
        </Space>
      ),
      children: (
        <div style={{ height: '60vh', display: 'flex', flexDirection: 'column' }}>
          <div style={{ marginBottom: '12px' }}>
            <Text type="secondary" style={{ fontSize: '13px' }}>
              编辑译文内容，保持与原文相同的结构和格式
            </Text>
          </div>
          <EnhancedTextArea
            value={translatedText}
            onChange={(e) => setTranslatedText(e.target.value)}
            placeholder="请输入译文内容..."
            style={{
              flex: 1,
              fontSize: '14px',
              lineHeight: '1.6',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              marginRight: '8px' // 给滚动条留出空间
            }}
            showCount={{
              formatter: ({ count }) => `${count} 字符`
            }}
          />
        </div>
      )
    }
  ];

  return (
    <Modal
      title={
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '0 4px'
        }}>
          <FileTextOutlined style={{ fontSize: '16px', color: '#1890ff' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>{title}</span>
          {hasChanges && (
            <div style={{
              background: 'linear-gradient(135deg, #fff7e6 0%, #ffecc7 100%)',
              border: '1px solid #ffd591',
              borderRadius: '12px',
              padding: '4px 8px',
              fontSize: '11px',
              color: '#d46b08'
            }}>
              <strong>有未保存的更改</strong>
            </div>
          )}
        </div>
      }
      open={open}
      onCancel={handleCancel}
      width={1000}
      style={{
        top: 20,
        maxHeight: 'calc(100vh - 40px)',
      }}
      footer={[
        <Button key="reset" onClick={handleReset} disabled={!hasChanges}>
          <UndoOutlined />
          重置
        </Button>,
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={handleSave}
          disabled={!hasChanges}
          icon={<SaveOutlined />}
        >
          保存更改
        </Button>
      ]}
      styles={{
        body: {
          padding: '16px 24px'
        }
      }}
      maskClosable={false}
    >
      <div style={{ marginBottom: '16px' }}>
        <Divider style={{ margin: '0 0 16px 0' }} />
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="small"
          tabBarStyle={{
            marginBottom: '16px'
          }}
        />
      </div>
    </Modal>
  );
};

export default FullTextEditModal;
