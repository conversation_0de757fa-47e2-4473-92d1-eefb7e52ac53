import React, { useState, useEffect } from 'react';
import { Button, Modal, Input, Space, message } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import {
  apiBaseUrl,
  defaultTranslationPromptText,
  defaultTitleGenerationPromptText,
  defaultSummaryGenerationPromptText,
  defaultParseTextPromptText,
  defaultReferenceParsePromptText
} from '../utils/constant.js';
import { useStore } from '../stores';
import EnhancedTextArea from './EnhancedTextArea';

const PromptButton = () => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [localTranslationPrompt, setLocalTranslationPrompt] = useState('');
  const [localTitleGenerationPrompt, setLocalTitleGenerationPrompt] = useState('');
  const [localSummaryGenerationPrompt, setLocalSummaryGenerationPrompt] = useState(''); // 新增AI总结提示词状态
  const [localParseTextPrompt, setLocalParseTextPrompt] = useState(''); // 新增解析文本提示词状态
  const [localReferenceParsePrompt, setLocalReferenceParsePrompt] = useState(''); // 新增参考文献解析提示词状态
  const [localGlossary, setLocalGlossary] = useState(''); // 新增术语库状态

  // 从 store 获取状态和新的 action
  const {
    translationPrompt,
    titleGenerationPrompt,
    summaryGenerationPrompt, // 获取新的AI总结提示词状态
    parseTextPrompt, // 获取新的解析文本提示词状态
    referenceParsePrompt, // 获取参考文献解析提示词状态
    glossary, // 获取术语库状态
    savePrompts,
    user
  } = useStore();

  // 打开modal时加载当前的prompt设置
  useEffect(() => {
    if (open) {
      setLocalTranslationPrompt(translationPrompt || defaultTranslationPromptText);
      setLocalTitleGenerationPrompt(titleGenerationPrompt || defaultTitleGenerationPromptText);
      setLocalSummaryGenerationPrompt(summaryGenerationPrompt || defaultSummaryGenerationPromptText); // 加载AI总结提示词
      setLocalParseTextPrompt(parseTextPrompt || defaultParseTextPromptText); // 加载解析文本提示词
      setLocalReferenceParsePrompt(referenceParsePrompt || defaultReferenceParsePromptText); // 加载参考文献解析提示词
      setLocalGlossary(glossary || ''); // 加载术语库
    }
  }, [open, translationPrompt, titleGenerationPrompt, summaryGenerationPrompt, parseTextPrompt, referenceParsePrompt, glossary]); // 添加referenceParsePrompt依赖

  // 处理保存按钮点击事件
  const handleSave = async () => {
    setIsLoading(true);
    const translationToSave = localTranslationPrompt;
    const titleGenerationToSave = localTitleGenerationPrompt;
    const summaryGenerationToSave = localSummaryGenerationPrompt; // 获取要保存的AI总结提示词
    const parseTextToSave = localParseTextPrompt; // 获取要保存的解析文本提示词
    const referenceParseToSave = localReferenceParsePrompt; // 获取要保存的参考文献解析提示词
    const glossaryToSave = localGlossary; // 获取要保存的术语库

    try {
      await savePrompts(translationToSave, titleGenerationToSave, summaryGenerationToSave, parseTextToSave, referenceParseToSave, glossaryToSave); // 传递参考文献解析提示词到action
      message.success('提示词设置已保存');
      setOpen(false);
    } catch (error) {
      message.error(`保存提示词设置失败: ${error.message || '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理各个提示词的重置函数
  const handleResetTranslation = () => {
    setLocalTranslationPrompt(defaultTranslationPromptText);
    message.info('翻译提示词已重置为默认值');
  };

  const handleResetTitleGeneration = () => {
    setLocalTitleGenerationPrompt(defaultTitleGenerationPromptText);
    message.info('标题生成提示词已重置为默认值');
  };

  const handleResetSummaryGeneration = () => {
    setLocalSummaryGenerationPrompt(defaultSummaryGenerationPromptText);
    message.info('AI总结提示词已重置为默认值');
  };

  const handleResetParseText = () => {
    setLocalParseTextPrompt(defaultParseTextPromptText);
    message.info('解析文本提示词已重置为默认值');
  };

  const handleResetReferenceParsePrompt = () => {
    setLocalReferenceParsePrompt(defaultReferenceParsePromptText);
    message.info('参考文献解析提示词已重置为默认值');
  };

  const handleResetGlossary = () => {
    setLocalGlossary('');
    message.info('术语库已清空');
  };

  return (
    <>
      <Button onClick={() => setOpen(true)} size="small" className="custom-small-font">提示词</Button>
      <Modal
        title="提示词设置"
        open={open}
        onCancel={() => setOpen(false)}
        width={700}
        style={{
          top: 20,
          maxHeight: 'calc(100vh - 40px)',
        }}
        footer={[
          <Button key="cancel" onClick={() => setOpen(false)}>
            取消
          </Button>,
          <Button key="save" type="primary" loading={isLoading} onClick={handleSave}>
            保存
          </Button>,
        ]}
      >
        <div style={{
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
          paddingRight: '16px',
          marginRight: '-8px'
        }}>
          <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <h4 style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>
                翻译提示词
                {localGlossary && localGlossary.trim() && (
                  <span style={{ fontSize: '12px', color: '#999', fontWeight: 'normal', marginLeft: '8px' }}>
                    （含术语库）
                  </span>
                )}
              </span>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleResetTranslation}
                style={{ color: '#666' }}
                title="重置为默认值"
              />
            </h4>
            <EnhancedTextArea
              value={localTranslationPrompt}
              onChange={(e) => setLocalTranslationPrompt(e.target.value)}
              autoSize={{ minRows: 3}}
              placeholder={defaultTranslationPromptText}
            />
          </div>
          <div>
            <h4 style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>
                标题生成提示词
                {localGlossary && localGlossary.trim() && (
                  <span style={{ fontSize: '12px', color: '#999', fontWeight: 'normal', marginLeft: '8px' }}>
                    （含术语库）
                  </span>
                )}
              </span>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleResetTitleGeneration}
                style={{ color: '#666' }}
                title="重置为默认值"
              />
            </h4>
            <EnhancedTextArea
              value={localTitleGenerationPrompt}
              onChange={(e) => setLocalTitleGenerationPrompt(e.target.value)}
              autoSize={{ minRows: 2,}}
              placeholder={defaultTitleGenerationPromptText}
            />
          </div>
          <div> {/* 新增AI总结提示词的输入区域 */}
            <h4 style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>
                AI总结提示词
                {localGlossary && localGlossary.trim() && (
                  <span style={{ fontSize: '12px', color: '#999', fontWeight: 'normal', marginLeft: '8px' }}>
                    （含术语库）
                  </span>
                )}
              </span>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleResetSummaryGeneration}
                style={{ color: '#666' }}
                title="重置为默认值"
              />
            </h4>
            <EnhancedTextArea
              value={localSummaryGenerationPrompt}
              onChange={(e) => setLocalSummaryGenerationPrompt(e.target.value)}
              autoSize={{ minRows: 2, }}
              placeholder={defaultSummaryGenerationPromptText}
            />
          </div>
          <div> {/* 新增解析文本提示词的输入区域 */}
            <h4 style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>解析文本提示词</span>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleResetParseText}
                style={{ color: '#666' }}
                title="重置为默认值"
              />
            </h4>
            <EnhancedTextArea
              value={localParseTextPrompt}
              onChange={(e) => setLocalParseTextPrompt(e.target.value)}
              autoSize={{ minRows: 3, }}
              placeholder={defaultParseTextPromptText}
            />
          </div>
          <div> {/* 新增参考文献解析提示词的输入区域 */}
            <h4 style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>参考文献解析提示词</span>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleResetReferenceParsePrompt}
                style={{ color: '#666' }}
                title="重置为默认值"
              />
            </h4>
            <EnhancedTextArea
              value={localReferenceParsePrompt}
              onChange={(e) => setLocalReferenceParsePrompt(e.target.value)}
              autoSize={{ minRows: 2,  }}
              placeholder={defaultReferenceParsePromptText}
            />
          </div>
          <div> {/* 新增术语库的输入区域 */}
            <h4 style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>术语库</span>
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleResetGlossary}
                style={{ color: '#666' }}
                title="清空术语库"
              />
            </h4>
            <EnhancedTextArea
              value={localGlossary}
              onChange={(e) => setLocalGlossary(e.target.value)}
              autoSize={{ minRows: 3, }}
              placeholder="请输入术语库内容，每行一个术语对应关系，例如：&#10;machine learning - 机器学习&#10;artificial intelligence - 人工智能&#10;&#10;术语库将自动附加到所有提示词的末尾。如果为空，则不会附加任何内容。"
            />
          </div>
        </Space>
        </div>
      </Modal>
    </>
  );
};

export default PromptButton;
