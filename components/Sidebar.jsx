import React, { useRef, useMemo } from 'react'; // Removed useEffect, useState, added useMemo
import { Layout, Menu, Button, Popconfirm, Avatar, Typography, Empty, App, Space, List } from 'antd'; // Removed Collapse, kept List
import { LogoutOutlined, DeleteOutlined, UserOutlined, FileTextOutlined, PlusSquareOutlined, LinkOutlined } from '@ant-design/icons'; // Removed CalendarOutlined
// Removed useNavigate, useParams from direct import as hooks will handle them
import { useStore } from '../stores/index.js'; // Added useStore back
import { shallow } from 'zustand/shallow'; // Added shallow
// Removed apiBaseUrl, getDocumentProxy, extractText, OpenAI, sidebarApiBaseUrl as they are in usePdfImport
import './Sidebar.css';

// Import custom hooks
import { usePdfImport } from '../hooks/usePdfImport';
import { useUserArticles } from '../hooks/useUserArticles';
import { useArticleCreation } from '../hooks/useArticleCreation';
import { useAuth } from '../hooks/useAuth';


const { Sider } = Layout;
const { Text } = Typography; // Removed Title as it's not used directly

// fileInputRefSidebar will be managed within usePdfImport or passed to it.
// For now, let's define it here and pass it to the hook.
const fileInputRefSidebar = React.createRef();

// 格式化日期为 YYYY-MM-DD 格式
const formatDate = (dateString) => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 按日期分组文章
const groupArticlesByDate = (articles) => {
  if (!Array.isArray(articles) || articles.length === 0) {
    return [];
  }

  const groups = {};

  articles.forEach(article => {
    const dateKey = new Date(article.createdAt).toDateString();
    if (!groups[dateKey]) {
      groups[dateKey] = {
        date: article.createdAt,
        articles: []
      };
    }
    groups[dateKey].articles.push(article);
  });

  // 转换为数组并按日期排序（最新的在前）
  return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date));
};

const Sidebar = () => {
  const { message: messageApiApp } = App.useApp();

  // Initialize custom hooks
  const {
    handlePdfFileSelect,
    triggerPdfUpload,
    retryFinalizeUpload, // Added from usePdfImport
    canRetryFinalize,    // Added from usePdfImport
    finalizeErrorDetails // Added from usePdfImport (though not directly used in this diff, good to have)
  } = usePdfImport(fileInputRefSidebar, messageApiApp); // Pass messageApiApp

  // Get PDF import status from store
  // Optimized useStore selectors to avoid returning new objects each time, addressing React DevTools warning.
  const isImportingPdf = useStore((state) => state.isImportingPdf);
  const importProgressMessage = useStore((state) => state.importProgressMessage);
  const currentImportController = useStore((state) => state.currentImportController);


  const {
    userArticles,
    isLoadingUserArticles,
    currentArticleUuidFromParams, // This now comes from useUserArticles
    deletingId,
    handleDeleteArticle,
    handleMenuClick,
    // fetchUserArticles // Exposed if needed for manual refresh, not used directly here yet
  } = useUserArticles();
  const { handleAddFromUrl, handleNewBlankArticle } = useArticleCreation();
  const { user, handleLogout, handleLoginNavigation } = useAuth();

  // 使用 useMemo 计算按日期分组的文章
  const groupedArticles = useMemo(() => {
    return groupArticlesByDate(userArticles);
  }, [userArticles]);

  // All logic previously in useEffects, handleDeleteArticle, handleLogout, handleMenuClick,
  // handlePdfFileSelectSidebar, triggerPdfUploadSidebar, handleAddFromUrl, handleNewBlankArticle
  // is now encapsulated within the custom hooks.

  // The main state like deletingId, user, userArticles, isLoadingUserArticles
  // are also provided by the custom hooks.

  return (
    <Sider
      width={250}
      style={{
        background: '#f5f5f5',
        borderRight: '1px solid #e8e8e8',
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 1000 // 将 zIndex 降低，确保 Modal 可以覆盖
      }}
    >
      {/* 上部区域: 包含操作按钮、标题和文章列表/Empty。 */}
      {/* 需要计算userInfoDiv的高度，为其设置paddingBottom */}
      <div style={{ overflowY: 'auto', height: '100%', paddingBottom: '80px' /* 预留给下方绝对定位的用户信息区 */ }}>
        {/* 新增的操作按钮区域 */}
        <div style={{ padding: '12px 8px', borderBottom: '1px solid #e8e8e8', textAlign: 'center' }}> {/* 调整外部 padding */}
          {/* 将隐藏的 input 移到 Space 组件外部，避免影响 Space 的子元素间距计算 */}
          <input
            type="file"
            ref={fileInputRefSidebar}
            onChange={handlePdfFileSelect} // Use hook's function
            style={{ display: 'none', width:0 }}
            accept=".pdf"
          />
          <Space direction="horizontal" align="center" size={8}>
            <Button
              type="text"
              onClick={handleAddFromUrl} // Use hook's function
              className="sidebar-action-button"
            >
              <LinkOutlined />
              <span>从URL添加</span>
            </Button>
            <Button
              type="text"
              onClick={triggerPdfUpload} // Use hook's function
              className="sidebar-action-button"
            >
              <FileTextOutlined />
              <span>从文件添加</span>
            </Button>
            <Button
              type="text"
              onClick={handleNewBlankArticle} // Use hook's function
              className="sidebar-action-button"
            >
              <PlusSquareOutlined />
              <span>新建文章</span>
            </Button>
          </Space>
        </div>

        {/* PDF Import Progress and Controls UI has been removed from here. */}
        {/* The logic remains in usePdfImport and related store state. */}
        <div style={{
            padding: (userArticles && userArticles.length > 0) ? '0' : '16px',
          }}>
          {isLoadingUserArticles ? (
            <div style={{ textAlign: 'center', padding: '10px' }}>加载中...</div>
          ) : userArticles && userArticles.length > 0 ? (
            <div style={{ padding: '8px' }}>
              {groupedArticles.map((group, groupIndex) => (
                <div key={groupIndex} style={{ marginBottom: '16px' }}>
                  {/* 日期标题 */}
                  <div style={{
                    padding: '4px 12px',
                    fontSize: '11px',
                    fontWeight: 400,
                    color: '#999',
                    marginBottom: '4px'
                  }}>
                    {formatDate(group.date)}
                  </div>

                  {/* 文章列表 */}
                  <List
                    size="small"
                    dataSource={group.articles}
                    renderItem={(article) => (
                      <List.Item
                        key={article.uuid}
                        style={{
                          padding: '4px 12px',
                          border: 'none',
                          cursor: 'pointer',
                          borderRadius: '4px',
                          backgroundColor: currentArticleUuidFromParams === article.uuid ? '#e6f7ff' : 'transparent'
                        }}
                        onClick={() => handleMenuClick({ key: article.uuid })}
                        onMouseEnter={(e) => {
                          if (currentArticleUuidFromParams !== article.uuid) {
                            e.currentTarget.style.backgroundColor = '#f0f0f0';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (currentArticleUuidFromParams !== article.uuid) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                          <div style={{ display: 'flex', alignItems: 'center', flexGrow: 1, minWidth: 0 }}>
                            <FileTextOutlined style={{ marginRight: '6px', color: '#666', fontSize: '14px' }} />
                            <Text
                              ellipsis={true}
                              title={article.title || '无标题文章'}
                              style={{ fontSize: '13px', color: '#333' }}
                            >
                              {article.title || '无标题文章'}
                            </Text>
                          </div>
                          <Popconfirm
                            title="确认删除这篇文章吗？"
                            description="此操作不可撤销。"
                            onConfirm={(e) => {
                              e.stopPropagation();
                              handleDeleteArticle(article.uuid);
                            }}
                            onCancel={(e) => e.stopPropagation()}
                            okText="删除"
                            cancelText="取消"
                            okButtonProps={{ loading: deletingId === article.uuid, danger: true }}
                          >
                            <Button
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                              loading={deletingId === article.uuid}
                              style={{
                                fontSize: '10px',
                                width: '20px',
                                height: '20px',
                                minWidth: '20px',
                                padding: 0
                              }}
                            />
                          </Popconfirm>
                        </div>
                      </List.Item>
                    )}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div style={{textAlign: 'center', paddingTop: '20px', paddingBottom: '20px'}}>
              <Empty description="暂无文章" />
            </div>
          )}
        </div>
      </div>

      <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          width: '100%',
          padding: '16px',
          borderTop: '1px solid #e0e0e0',
          background: '#f5f5f5',
          boxSizing: 'border-box'
      }}>
        {user ? ( // user from useAuth
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Avatar icon={user.avatar ? <img src={user.avatar} alt="avatar" /> : <UserOutlined />} style={{ marginRight: 8 }} />
              <Text ellipsis={true} title={user.nickname || user.name || user.username || '未登录'}>
                {user.nickname || user.name || user.username || '未登录'}
              </Text>
            </div>
            <Popconfirm
              title="确认退出登录吗？"
              onConfirm={handleLogout} // Use hook's function
              okText="确认"
              cancelText="取消"
            >
              <Button icon={<LogoutOutlined />} type="text" danger title="退出登录" />
            </Popconfirm>
          </div>
        ) : (
          <Button type="primary" block onClick={handleLoginNavigation}> {/* Use hook's function */}
            登录 / 注册
          </Button>
        )}
      </div>
    </Sider>
  );
};

export default Sidebar;